"""
Test Constants Integration
Verifies that all hardcoded values have been successfully replaced with constants.
"""

import sys
import asyncio
from constants import (
    URLs, Timeouts, Browser, UserData, Localization, 
    Headers, AntiDetection, Paths, Analytics, Messages, BuildInfo
)
from config import Config
from user_data_generator import UserDataGenerator
from browser_profile import <PERSON>rowserProfileGenerator
from human_behavior import <PERSON><PERSON><PERSON><PERSON><PERSON>


def test_constants_structure():
    """Test that all constants classes have required attributes"""
    print("🧪 Testing Constants Structure...")
    
    # Test URLs
    assert hasattr(URLs, 'GMAIL_SIGNUP')
    assert hasattr(URLs, 'IP_CHECK_SERVICES')
    print("✅ URLs constants OK")
    
    # Test Timeouts
    assert hasattr(Timeouts, 'BROWSER_DEFAULT')
    assert hasattr(Timeouts, 'HUMAN_ACTION')
    assert hasattr(Timeouts, 'HUMAN_TYPING')
    print("✅ Timeouts constants OK")
    
    # Test Browser
    assert hasattr(<PERSON>rowser, 'VIEWPORT_SIZES')
    assert hasattr(Browser, 'CHROME_ARGS')
    assert hasattr(<PERSON>rowser, 'FALL<PERSON>CK_USER_AGENT')
    print("✅ Browser constants OK")
    
    # Test UserData
    assert hasattr(UserData, 'AMERICAN_FIRST_NAMES_MALE')
    assert hasattr(UserData, 'PASSWORD_CONFIG')
    assert hasattr(UserData, 'US_AREA_CODES')
    print("✅ UserData constants OK")
    
    # Test Messages
    assert hasattr(Messages, 'CONSOLE')
    assert hasattr(Messages, 'ERRORS')
    print("✅ Messages constants OK")
    
    print("🎉 All constants structure tests passed!")


def test_config_integration():
    """Test that Config class uses constants properly"""
    print("\n🧪 Testing Config Integration...")
    
    # Test that Config uses constants
    assert Config.BROWSER_TIMEOUT == Timeouts.BROWSER_DEFAULT
    assert Config.US_TIMEZONES == Localization.US_TIMEZONES
    assert Config.CHROME_ARGS == Browser.CHROME_ARGS
    assert Config.US_AREA_CODES == UserData.US_AREA_CODES
    
    print("✅ Config integration with constants OK")


def test_user_data_generation():
    """Test that UserDataGenerator uses constants"""
    print("\n🧪 Testing UserData Generation...")
    
    # Generate a user
    user = UserDataGenerator.generate_user_info()
    
    # Verify required fields exist
    required_fields = [
        'first_name', 'last_name', 'username', 'password',
        'birth_year', 'birth_month', 'birth_day', 'phone', 'timezone'
    ]
    
    for field in required_fields:
        assert field in user, f"Missing field: {field}"
    
    # Verify timezone is from constants
    assert user['timezone'] in Localization.US_TIMEZONES
    
    # Verify password meets requirements
    password = user['password']
    assert len(password) >= UserData.PASSWORD_CONFIG['min_length']
    
    print(f"✅ Generated user: {user['full_name']} ({user['username']})")
    print("✅ UserData generation with constants OK")


def test_browser_profile_generation():
    """Test that BrowserProfileGenerator uses constants"""
    print("\n🧪 Testing Browser Profile Generation...")
    
    generator = BrowserProfileGenerator()
    profile = generator.create_human_browser_profile()
    
    # Verify profile structure
    assert 'viewport' in profile
    assert 'timezone_id' in profile
    assert 'locale' in profile
    assert 'chromium_args' in profile
    
    # Verify timezone is from constants
    assert profile['timezone_id'] in Localization.US_TIMEZONES
    
    # Verify locale is from constants
    assert profile['locale'] == Localization.LOCALE
    
    # Verify viewport is from constants
    assert profile['viewport'] in Browser.VIEWPORT_SIZES or profile['viewport'] == Browser.DEFAULT_VIEWPORT
    
    print(f"✅ Generated profile with timezone: {profile['timezone_id']}")
    print("✅ Browser profile generation with constants OK")


async def test_human_behavior():
    """Test that HumanBehavior uses constants"""
    print("\n🧪 Testing Human Behavior...")
    
    # Test human_delay uses default constants
    import time
    start = time.time()
    await HumanBehavior.human_delay()  # Should use default from constants
    duration = time.time() - start
    
    # Should be within expected range from constants
    assert Timeouts.HUMAN_ACTION['min'] <= duration <= Timeouts.HUMAN_ACTION['max'] + 0.5  # +0.5 for tolerance
    
    # Test simulate_thinking uses default constants
    start = time.time()
    await HumanBehavior.simulate_thinking()  # Should use default from constants
    duration = time.time() - start
    
    assert Timeouts.HUMAN_THINKING['min'] <= duration <= Timeouts.HUMAN_THINKING['max'] + 0.5
    
    print("✅ Human behavior timing with constants OK")


def test_messages_integration():
    """Test that Messages constants are properly structured"""
    print("\n🧪 Testing Messages Integration...")
    
    # Test console messages
    console_messages = Messages.CONSOLE
    required_console_keys = [
        'init_start', 'init_success', 'init_failed',
        'registration_start', 'registration_success', 'registration_failed',
        'user_generated', 'proxy_using'
    ]
    
    for key in required_console_keys:
        assert key in console_messages, f"Missing console message: {key}"
        assert isinstance(console_messages[key], str), f"Console message {key} should be string"
    
    # Test error messages
    error_messages = Messages.ERRORS
    required_error_keys = ['no_proxy', 'registration_failed', 'unknown_error']
    
    for key in required_error_keys:
        assert key in error_messages, f"Missing error message: {key}"
        assert isinstance(error_messages[key], str), f"Error message {key} should be string"
    
    print("✅ Messages constants structure OK")


def test_build_info():
    """Test BuildInfo constants"""
    print("\n🧪 Testing Build Info...")
    
    assert hasattr(BuildInfo, 'VERSION')
    assert hasattr(BuildInfo, 'BUILD_DATE')
    assert hasattr(BuildInfo, 'AUTHOR')
    assert hasattr(BuildInfo, 'DESCRIPTION')
    
    print(f"✅ Build Info: {BuildInfo.DESCRIPTION} v{BuildInfo.VERSION}")


def print_summary():
    """Print summary of constants usage"""
    print("\n" + "="*60)
    print("📊 CONSTANTS INTEGRATION SUMMARY")
    print("="*60)
    
    print(f"🌐 URLs defined: {len(URLs.IP_CHECK_SERVICES)} IP check services")
    print(f"⏰ Timeouts: {len([attr for attr in dir(Timeouts) if not attr.startswith('_')])} timeout categories")
    print(f"🌍 Browser profiles: {len(Browser.VIEWPORT_SIZES)} viewport sizes")
    print(f"👤 User data: {len(UserData.AMERICAN_FIRST_NAMES_MALE + UserData.AMERICAN_FIRST_NAMES_FEMALE)} names")
    print(f"🏙️ US Cities: {len(UserData.US_CITIES)} cities")
    print(f"📞 Area codes: {len(UserData.US_AREA_CODES)} area codes")
    print(f"🕒 Timezones: {len(Localization.US_TIMEZONES)} US timezones")
    print(f"💬 Console messages: {len(Messages.CONSOLE)} messages")
    print(f"❌ Error messages: {len(Messages.ERRORS)} error types")
    print(f"🛡️ Stealth scripts: {len(AntiDetection.STEALTH_SCRIPTS)} anti-detection scripts")
    
    print("\n✅ All hardcoded values successfully replaced with configurable constants!")
    print("🎯 Project is now much more maintainable and flexible.")


async def main():
    """Run all tests"""
    print("🚀 Starting Constants Integration Tests...\n")
    
    try:
        # Run synchronous tests
        test_constants_structure()
        test_config_integration()
        test_user_data_generation()
        test_browser_profile_generation()
        test_messages_integration()
        test_build_info()
        
        # Run asynchronous tests
        await test_human_behavior()
        
        # Print summary
        print_summary()
        
        print(f"\n🎉 ALL TESTS PASSED! Constants integration successful.")
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # Run tests
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 