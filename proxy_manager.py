"""
Proxy Management Module
Handles proxy rotation, testing, and selection to ensure reliable connections.
"""

import asyncio
import random
import httpx
import time
from typing import List, Optional, Dict, Set, Any
from config import Config, ProxyConfig
from constants import URLs, Timeouts
from dataclasses import dataclass, field


@dataclass
class ProxyStatus:
    """Tracks the status and performance of a single proxy."""
    proxy: ProxyConfig
    is_working: bool = True
    last_tested_timestamp: float = 0.0
    response_time: float = 0.0
    success_count: int = 0
    failure_count: int = 0
    last_error: str = ""

class ProxyManager:
    """Manages the lifecycle of proxies: loading, testing, rotation, and stats."""
    
    def __init__(self):
        self.all_proxies: List[ProxyStatus] = [ProxyStatus(p) for p in Config.PROXY_LIST]
        self.working_proxies: List[ProxyStatus] = []
        self._current_proxy_index = 0
        
    async def initial_setup(self) -> bool:
        """
        Tests all proxies from the config and populates the list of working proxies.
        This should be run once at application startup.

        Returns:
            bool: True if at least one proxy is working, False otherwise.
        """
        print(f"🚀 Initializing proxy manager...")
        if not self.all_proxies:
            print("⚠️ No proxies found in configuration.")
            return True  # No proxies to use, but not a failure state

        try:
            await self.test_all_proxies()
            
            self.working_proxies = [p for p in self.all_proxies if p.is_working]
            
            if not self.working_proxies:
                print("❌ 0/{} proxies working. No working proxies found!".format(len(self.all_proxies)))
                return False
                
            print(f"✅ Proxy setup complete. {len(self.working_proxies)}/{len(self.all_proxies)} proxies are working.")
            self.print_proxy_status()
            return True
        except Exception as e:
            print(f"❌ Initialization failed: {str(e)}")
            return False

    async def test_all_proxies(self):
        """Tests all configured proxies concurrently and updates their statuses."""
        print(f"🔍 Testing {len(self.all_proxies)} proxies...")
        tasks = [self._test_proxy(proxy_status) for proxy_status in self.all_proxies]
        await asyncio.gather(*tasks)

    async def _test_proxy(self, proxy_status: ProxyStatus):
        """
        Tests a single proxy against a set of test URLs and updates its status.

        Args:
            proxy_status: The ProxyStatus object to test.
        """
        test_url = random.choice(URLs.IP_CHECK_SERVICES)
        proxy = proxy_status.proxy
        
        # Format proxy URL with auth if provided
        auth = f"{proxy.username}:{proxy.password}@" if proxy.username and proxy.password else ""
        proxy_url = f"http://{auth}{proxy.server}"
        
        # Create transport with proxy configuration
        transport = httpx.AsyncHTTPTransport(
            proxy=httpx.Proxy(url=proxy_url)
        )
        
        start_time = time.time()
        try:
            async with httpx.AsyncClient(transport=transport, verify=False, timeout=Timeouts.PROXY_TEST) as client:
                response = await client.get(test_url)
                response.raise_for_status()

                # If the test is successful
                end_time = time.time()
                proxy_status.is_working = True
                proxy_status.response_time = end_time - start_time
                proxy_status.success_count += 1
                proxy_status.last_error = ""
                print(f"✅ Proxy {proxy.server} passed test in {proxy_status.response_time:.2f}s")

        except (httpx.RequestError, httpx.HTTPStatusError) as e:
            proxy_status.is_working = False
            proxy_status.failure_count += 1
            proxy_status.last_error = str(e)
            print(f"❌ Proxy {proxy.server} failed test. Error: {e}")
        finally:
            proxy_status.last_tested_timestamp = time.time()


    async def get_working_proxy(self) -> Optional[ProxyConfig]:
        """
        Returns a working proxy using a round-robin strategy.
        If a proxy fails, it's marked as non-working until the next full test.

        Returns:
            Optional[ProxyConfig]: A working proxy configuration or None if none are available.
        """
        if not self.working_proxies:
            print("⚠️ No working proxies available.")
            return None

        # Simple round-robin
        self._current_proxy_index = (self._current_proxy_index + 1) % len(self.working_proxies)
        selected_proxy_status = self.working_proxies[self._current_proxy_index]
        
        print(f"🌐 Selected proxy {selected_proxy_status.proxy.server} for use.")
        return selected_proxy_status.proxy

    def get_proxy_stats(self) -> Dict[str, Any]:
        """
        Calculates and returns statistics about the proxy pool.

        Returns:
            Dict[str, Any]: A dictionary containing proxy statistics.
        """
        total_proxies = len(self.all_proxies)
        num_working = len(self.working_proxies)
        
        if total_proxies == 0:
            return {
                "total_proxies": 0, "working_proxies": 0, "failed_proxies": 0,
                "success_rate": 100.0, "average_response_time": 0
            }

        avg_response_time = 0
        if num_working > 0:
            total_response_time = sum(p.response_time for p in self.working_proxies if p.response_time > 0)
            avg_response_time = total_response_time / num_working

        return {
            "total_proxies": total_proxies,
            "working_proxies": num_working,
            "failed_proxies": total_proxies - num_working,
            "success_rate": (num_working / total_proxies) * 100 if total_proxies > 0 else 0,
            "average_response_time": avg_response_time
        }

    def print_proxy_status(self):
        """Prints a formatted report of the current status of all proxies."""
        stats = self.get_proxy_stats()
        
        print("\n📊 Proxy Status Report:")
        print(f"  - Total Proxies: {stats['total_proxies']}")
        print(f"  - ✅ Working: {stats['working_proxies']}")
        print(f"  - ❌ Failed: {stats['failed_proxies']}")
        print(f"  - 📈 Success Rate: {stats['success_rate']:.1f}%")
        print(f"  - ⏱️  Avg Response Time: {stats['average_response_time']:.2f}s")
        
        if self.all_proxies:
            print("\n🔍 Detailed Status:")
            for proxy_status in self.all_proxies:
                status_icon = "✅" if proxy_status.is_working else "❌"
                print(f"  {status_icon} {proxy_status.proxy.server:<25}"
                      f"| Responded in: {proxy_status.response_time:.2f}s "
                      f"| Success: {proxy_status.success_count}, Fails: {proxy_status.failure_count}")
                if proxy_status.last_error:
                    print(f"    └── Last Error: {proxy_status.last_error}") 