#!/usr/bin/env python3
"""
Gmail Auto Registration Demo
Demonstrates the system functionality without requiring actual API keys or proxies.
"""

import asyncio
from datetime import datetime

# Import test config first to override settings
import test_config

# Import our modules after test config
from user_data_generator import UserDataGenerator
from browser_profile import BrowserProfileGenerator
from human_behavior import HumanBehavior


async def demo_user_generation():
    """Demo user data generation"""
    print("\n🎯 DEMO: User Data Generation")
    print("=" * 50)
    
    generator = UserDataGenerator()
    
    # Generate single user
    user = generator.generate_user_info()
    generator.print_user_info(user)
    
    # Validate user data
    is_valid = generator.validate_user_data(user)
    print(f"\n✅ User data validation: {'PASSED' if is_valid else 'FAILED'}")
    
    return user


async def demo_browser_profile(user_data):
    """Demo browser profile creation"""
    print("\n🎯 DEMO: Browser Profile Creation")
    print("=" * 50)
    
    generator = BrowserProfileGenerator()
    
    # Create profile without proxy
    profile = generator.create_human_browser_profile()
    
    print(f"🌐 User Agent: {profile['user_agent'][:70]}...")
    print(f"📱 Viewport: {profile['viewport']}")
    print(f"🌍 Timezone: {profile['timezone_id']}")
    print(f"🎭 Stealth Mode: {profile['stealth']}")
    print(f"⚙️ Chrome Args: {len(profile['chromium_args'])} arguments")
    print(f"🔒 Headless: {profile['headless']}")
    
    return profile


async def demo_human_behavior():
    """Demo human behavior simulation"""
    print("\n🎯 DEMO: Human Behavior Simulation")
    print("=" * 50)
    
    print("⏰ Simulating human delays...")
    await HumanBehavior.human_delay(1, 2)
    
    print("💭 Simulating thinking pause...")
    await HumanBehavior.simulate_thinking(0.5, 1.0)
    
    print("😴 Checking for distraction simulation...")
    await HumanBehavior.simulate_distraction()
    
    print("✅ Human behavior simulation completed")


async def demo_registration_flow(user_data, browser_profile):
    """Demo complete registration flow"""
    print("\n🎯 DEMO: Complete Registration Flow")
    print("=" * 50)
    
    start_time = datetime.now()
    
    print(f"👤 User: {user_data['full_name']} ({user_data['username']})")
    print(f"🌍 Browser: {browser_profile['timezone_id']}, {browser_profile['viewport']}")
    print(f"📧 Target Email: {user_data['username']}@gmail.com")
    
    # Simulate registration steps
    steps = [
        ("🌐 Starting browser session", 2, 3),
        ("📍 Navigating to Gmail signup", 3, 5),
        ("📝 Filling first name field", 1, 2),
        ("📝 Filling last name field", 1, 2),
        ("📝 Filling username field", 2, 3),
        ("📝 Filling password field", 2, 4),
        ("📝 Confirming password", 1, 2),
        ("📅 Selecting birth date", 2, 3),
        ("📱 Entering phone number", 2, 3),
        ("🤖 Handling CAPTCHA", 5, 15),
        ("📱 Processing verification", 5, 10),
        ("✅ Completing registration", 2, 5),
    ]
    
    for step_name, min_delay, max_delay in steps:
        print(f"   {step_name}...")
        await HumanBehavior.human_delay(min_delay * 0.1, max_delay * 0.1)  # Speed up for demo
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print(f"\n🎉 Registration simulation completed!")
    print(f"📧 Simulated Email: {user_data['username']}@gmail.com")
    print(f"⏱️  Duration: {duration:.1f} seconds")
    print(f"🔒 Password: {user_data['password']}")
    
    return {
        "success": True,
        "email": f"{user_data['username']}@gmail.com",
        "duration": duration,
        "user_data": user_data
    }


async def demo_batch_simulation():
    """Demo batch account generation"""
    print("\n🎯 DEMO: Batch Account Simulation")
    print("=" * 50)
    
    num_accounts = 3
    print(f"🚀 Simulating {num_accounts} account registrations...")
    
    generator = UserDataGenerator()
    users = generator.generate_batch_users(num_accounts)
    
    for i, user in enumerate(users, 1):
        print(f"\n--- Account {i} ---")
        print(f"Name: {user['full_name']}")
        print(f"Username: {user['username']}")
        print(f"Email: {user['username']}@gmail.com")
        print(f"Phone: {user['phone']}")
        print(f"City: {user['city']}")
    
    print(f"\n✅ Generated {len(users)} unique accounts")


async def main():
    """Main demo function"""
    print("🤖 Gmail Auto Registration - DEMO MODE")
    print("=" * 70)
    print("⚠️  This is a demonstration only - no actual registration occurs")
    print("=" * 70)
    
    try:
        # Demo 1: User Generation
        user_data = await demo_user_generation()
        
        # Demo 2: Browser Profile
        browser_profile = await demo_browser_profile(user_data)
        
        # Demo 3: Human Behavior
        await demo_human_behavior()
        
        # Demo 4: Registration Flow
        result = await demo_registration_flow(user_data, browser_profile)
        
        # Demo 5: Batch Simulation
        await demo_batch_simulation()
        
        # Summary
        print("\n🎯 DEMO SUMMARY")
        print("=" * 50)
        print("✅ User data generation: Working")
        print("✅ Browser profile creation: Working") 
        print("✅ Human behavior simulation: Working")
        print("✅ Registration flow simulation: Working")
        print("✅ Batch processing: Working")
        
        print("\n📋 NEXT STEPS FOR REAL USAGE:")
        print("1. Set up API keys (OPENAI_API_KEY or ANTHROPIC_API_KEY)")
        print("2. Configure proxies in config.py (recommended)")
        print("3. Test with: python main.py --test-mode")
        print("4. Run actual registration: python main.py --accounts 1")
        
        print("\n🎉 Demo completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main()) 