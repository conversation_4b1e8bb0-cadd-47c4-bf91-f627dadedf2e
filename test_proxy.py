import requests
import time
from urllib.parse import urlparse

def test_proxy(proxy_url):
    # Parse proxy URL
    parsed = urlparse(proxy_url)
    username = parsed.username
    password = parsed.password
    host = parsed.hostname
    port = parsed.port
    
    # Format proxy for requests
    proxy = f"socks5://{username}:{password}@{host}:{port}"
    proxies = {
        'http': proxy,
        'https': proxy
    }
    
    test_urls = [
        'http://ip-api.com/json',  # To check IP info
        'https://www.google.com',  # To test HTTPS connection
    ]
    
    print(f"Testing proxy: {host}:{port}")
    print(f"Username: {username}")
    print("-" * 50)
    
    for url in test_urls:
        try:
            start_time = time.time()
            response = requests.get(url, proxies=proxies, timeout=10)
            elapsed = time.time() - start_time
            
            print(f"\nTesting connection to: {url}")
            print(f"Status code: {response.status_code}")
            print(f"Response time: {elapsed:.2f} seconds")
            
            if 'ip-api.com' in url:
                data = response.json()
                print("\nProxy IP Information:")
                print(f"IP: {data.get('query')}")
                print(f"Location: {data.get('city')}, {data.get('regionName')}, {data.get('country')}")
                print(f"ISP: {data.get('isp')}")
                
        except Exception as e:
            print(f"\nError testing {url}:")
            print(f"Error message: {str(e)}")
            
if __name__ == "__main__":
    proxy_url = "socks5://nfquyrazih-res-country-US-state-5001836-city-4990729-hold-session-session-686bcbe2304d5:XiiAaPPcc4nKYD1I@*************:9999"
    test_proxy(proxy_url) 