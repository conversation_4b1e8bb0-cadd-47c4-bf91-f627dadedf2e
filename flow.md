# Quy trình đăng ký Gmail tự động

## <PERSON><PERSON><PERSON> bị

1. Khởi tạo trình duyệt với cấu hình:

   - Headless mode: false
   - Viewport: 1280x720
   - User Agent: Chrome trên Windows
   - Locale: en-US
   - Timezone: America/New_York

2. <PERSON><PERSON><PERSON> bị dữ liệu người dùng:
   - First name: John
   - Last name: Doe
   - Username: jd2262829 (sau khi johndoe2024 bị trùng)
   - Password: StrongP@ss123
   - Birth date: March 15, 1990
   - Gender: Male

## <PERSON><PERSON><PERSON> bước thực hiện

1. T<PERSON><PERSON> cập trang đăng ký:

   ```python
   await page.goto('https://accounts.google.com/signup')
   ```

2. Điền thông tin cơ bản:

   ```python
   # Điền First name
   await page.getByRole('textbox', { name: 'First name' }).fill('<PERSON>')

   # Điền Last name
   await page.getByRole('textbox', { name: 'Last name (optional)' }).fill('Doe')

   # Click Next
   await page.getByRole('button', { name: 'Next' }).click()
   ```

3. Đi<PERSON>n thông tin cá nhân:

   ```python
   # Chọn tháng sinh
   await page.getByRole('combobox', { name: 'Month' }).click()
   await page.getByRole('option', { name: 'March' }).click()

   # Điền ngày sinh
   await page.getByRole('textbox', { name: 'Day' }).fill('15')

   # Điền năm sinh
   await page.getByRole('textbox', { name: 'Year' }).fill('1990')

   # Chọn giới tính
   await page.getByRole('combobox', { name: 'Gender' }).click()
   await page.getByRole('option', { name: 'Male' }).click()

   # Click Next
   await page.getByRole('button', { name: 'Next' }).click()
   ```

4. Điền username và password:

   ```python
   # Điền username
   await page.getByRole('textbox', { name: 'Username' }).fill('johndoe2024')

   # Xử lý username đã tồn tại
   error_text = await page.getByText('That username is taken. Try another.').isVisible()
   if error_text:
       # Chọn username được đề xuất
       await page.getByRole('button', { name: 'jd2262829' }).click()

   # Click Next
   await page.getByRole('button', { name: 'Next' }).click()

   # Điền password
   await page.getByRole('textbox', { name: 'Password' }).fill('StrongP@ss123')

   # Xác nhận password
   await page.getByRole('textbox', { name: 'Confirm' }).fill('StrongP@ss123')

   # Click Next
   await page.getByRole('button', { name: 'Next' }).click()
   ```

## Xử lý lỗi và ngoại lệ

1. Kiểm tra username đã tồn tại:

   ```python
   error_text = await page.getByText('That username is taken. Try another.').isVisible()
   if error_text:
       # Sử dụng username được đề xuất
       suggested_username = await page.getByRole('button', { name: /^jd\d+$/ }).textContent()
       await page.getByRole('button', { name: suggested_username }).click()
   ```

2. Xử lý captcha:

   ```python
   captcha_frame = await page.frameLocator('iframe[title="reCAPTCHA"]')
   if await captcha_frame.isVisible():
       # Chụp ảnh captcha
       await captcha_frame.screenshot(path='captcha.png')
       # Lưu log
       print("⚠️ Captcha detected")
   ```

3. Xử lý timeout:
   ```python
   try:
       await page.waitForSelector('button[name="Next"]', timeout=30000)
   except TimeoutError:
       print("⚠️ Timeout waiting for Next button")
       # Thử lại
       await page.reload()
   ```

## Ghi log và theo dõi

1. Chụp ảnh màn hình:

   ```python
   # Sau mỗi bước
   await page.screenshot(path=f'step_{step_number}.png')

   # Khi gặp lỗi
   if error:
       await page.screenshot(path=f'error_{error_type}.png')
   ```

2. Ghi log chi tiết:
   ```python
   logging.info(f"Step {step_number} completed in {duration}s")
   if error:
       logging.error(f"Error: {error_message}")
   ```

## Kết quả

- Thời gian thực hiện: 120 giây
- Trạng thái: Đang thực hiện
- Các bước đã hoàn thành:
  - ✅ Truy cập trang đăng ký
  - ✅ Điền thông tin cơ bản
  - ✅ Điền thông tin cá nhân
  - ✅ Điền username và password
  - ⏳ Xác minh số điện thoại
  - ⏳ Xác nhận và hoàn tất

## Các vấn đề gặp phải

1. Username đã tồn tại:

   - Vấn đề: Username "johndoe2024" đã được sử dụng
   - Giải pháp: Sử dụng username được đề xuất "jd2262829"

2. Xác minh số điện thoại:
   - Vấn đề: Google yêu cầu xác minh số điện thoại
   - Giải pháp: Cần thêm logic để xử lý bước xác minh này
