# Thư viện Browser-Use

Browser-Use là một thư viện tự động hóa trình duyệt được thiết kế đặc biệt cho các agent AI. Thư viện này cung cấp một giao diện đơn giản để điều khiển trình duyệt web thông qua các API dễ sử dụng.

## Cài đặt

```bash
pip install browser-use
```

## Cấu hình môi trường

Tạo file `.env` với các API key cần thiết:

```bash
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
BROWSER_USE_LOGGING_LEVEL=debug  # Tùy chọn cho development
```

## Sử dụng cơ bản

```python
from browser_use import Agent
from browser_use.llm import ChatOpenAI
from dotenv import load_dotenv

load_dotenv()

# Khởi tạo model LLM
llm = ChatOpenAI(model="gpt-4.1")

# Tạo agent
agent = Agent(
    task="Go to google.com and search for Browser Use",
    llm=llm,
)

# Chạy agent
async def main():
    result = await agent.run()
    print(result)

asyncio.run(main())
```

## Tính năng chính

1. **Tự động hóa trình duyệt**: Điều khiển trình duyệt web thông qua API đơn giản
2. **Tích hợp LLM**: Hỗ trợ nhiều mô hình ngôn ngữ lớn như OpenAI GPT, Anthropic Claude
3. **Quản lý phiên**: Cho phép lưu và tải lại trạng thái phiên làm việc
4. **Tùy chỉnh cấu hình**: Cấu hình chi tiết cho trình duyệt và agent
5. **Xử lý dữ liệu nhạy cảm**: Cơ chế bảo vệ thông tin nhạy cảm

## Cấu hình chi tiết

### BrowserProfile

```python
browser_profile = BrowserProfile(
    headless=False,
    storage_state="path/to/storage_state.json",
    viewport={"width": 1280, "height": 1100},
    locale='en-US',
    user_agent='Mozilla/5.0...',
    highlight_elements=True,
    viewport_expansion=500,
    allowed_domains=['*.google.com'],
    user_data_dir=None,
)
```

### BrowserSession

```python
browser_session = BrowserSession(
    browser_profile=browser_profile,
    headless=True  # Ghi đè cấu hình từ profile
)
```

## Xử lý dữ liệu nhạy cảm

```python
sensitive_data = {
    'https://*.example.com': {
        'x_username': 'user123',
        'x_password': 'pass123',
    },
}

agent = Agent(
    task="Login to example.com",
    llm=llm,
    sensitive_data=sensitive_data,
    browser_session=BrowserSession(
        allowed_domains=['https://*.example.com']
    ),
    use_vision=False,  # Tắt vision để tránh lộ thông tin nhạy cảm
)
```

## Hooks

```python
async def my_step_hook(agent: Agent):
    page = await agent.browser_session.get_current_page()
    current_url = page.url
    print(f"Agent đang ở trang: {current_url}")

agent = Agent(
    task="Your task",
    llm=llm,
    on_step_start=my_step_hook,
)
```

## Chạy nhiều agent song song

```python
async with async_playwright() as playwright:
    browser_context = await playwright.chromium.launch_persistent_context()
    page1 = await browser_context.new_page()
    page2 = await browser_context.new_page()

    agent1 = Agent(task="Task 1", llm=llm, page=page1)
    agent2 = Agent(task="Task 2", llm=llm, page=page2)

    await asyncio.gather(agent1.run(), agent2.run())
```

## Lưu ý quan trọng

1. Luôn sử dụng `async/await` khi làm việc với thư viện
2. Cẩn thận với dữ liệu nhạy cảm, sử dụng `use_vision=False` khi cần thiết
3. Giới hạn domain cho browser session để tăng tính bảo mật
4. Sử dụng hooks để theo dõi và kiểm soát quá trình thực thi
5. Backup dữ liệu quan trọng trước khi thực hiện các thao tác nguy hiểm

## Tài liệu tham khảo

- [Trang chủ Browser-Use](https://github.com/browser-use/browser-use)
- [Tài liệu API](https://api.browser-use.com/docs)
- [Ví dụ mẫu](https://github.com/browser-use/browser-use/tree/main/examples)
