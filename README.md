# 🤖 Gmail Auto Registration with <PERSON>rowser-Use

An advanced Gmail account registration automation system using Browser-Use AI agents with human-like behavior and anti-detection techniques.

## ⚠️ **IMPORTANT LEGAL DISCLAIMER**

**This tool is for educational and testing purposes only.**

- Automating Gmail registration may violate Google's Terms of Service
- Use only for legitimate testing and development purposes
- Ensure compliance with applicable laws and regulations
- The authors are not responsible for misuse of this software

## 🎯 Features

- ✅ **AI-Powered Registration**: Uses Browser-Use AI agents for intelligent form filling
- ✅ **Human-like Behavior**: Realistic typing, mouse movements, and delays
- ✅ **Anti-Detection**: Advanced techniques to avoid bot detection
- ✅ **Proxy Support**: Built-in proxy rotation and health checking
- ✅ **US Timezone Support**: Configured for American users and timezones
- ✅ **Batch Processing**: Register multiple accounts concurrently
- ✅ **Comprehensive Logging**: Detailed logging and result tracking
- ✅ **Error Handling**: Robust error handling and retry mechanisms

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd gmail-auto-registration

# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install chromium
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

**Required Environment Variables:**

```bash
# At least one LLM API key
OPENAI_API_KEY=your_openai_key_here
# OR
ANTHROPIC_API_KEY=your_anthropic_key_here

# Optional: Proxy settings
USE_PROXY=true
# Add your proxies to config.py PROXY_LIST

# Optional: Other settings
HEADLESS_MODE=false
MAX_CONCURRENT_REGISTRATIONS=2
DELAY_BETWEEN_REGISTRATIONS=300
```

### 3. Setup Proxies (Optional but Recommended)

Edit `config.py` and add your proxies:

```python
PROXY_LIST: List[ProxyConfig] = [
    ProxyConfig("proxy1.example.com:8080", "username1", "password1"),
    ProxyConfig("proxy2.example.com:8080", "username2", "password2"),
    # Add more proxies...
]
```

### 4. Run the System

```bash
# Test configuration
python main.py --config-check

# Test proxies
python main.py --proxy-test

# Test mode (no actual registration)
python main.py --test-mode --accounts 1

# Register single account
python main.py --accounts 1

# Register multiple accounts
python main.py --accounts 5 --concurrent 2
```

## 📋 Command Line Options

```bash
python main.py [OPTIONS]

Options:
  -n, --accounts INTEGER     Number of accounts to register [default: 1]
  -c, --concurrent INTEGER   Max concurrent registrations
  -t, --test-mode           Run in test mode (no actual registration)
  --config-check            Check configuration and exit
  --proxy-test              Test proxies and exit
  --help                    Show this message and exit
```

## 🏗️ Project Structure

```
gmail-auto-registration/
├── config.py              # Configuration management
├── main.py                 # Main entry point
├── requirements.txt        # Python dependencies
├── plan.md                # Detailed project plan
├──
├── core modules/
│   ├── user_data_generator.py  # Realistic user data generation
│   ├── proxy_manager.py        # Proxy rotation and management
│   ├── browser_profile.py      # Human-like browser profiles
│   └── human_behavior.py       # Human behavior simulation
├──
├── data/                   # Generated data and results
│   ├── gmail_accounts.json    # Successful registrations
│   └── batch_results_*.json   # Batch operation results
├──
├── logs/                   # Application logs
├── screenshots/            # Debug screenshots
└── conversations/          # Browser-Use conversations
```

## 🔧 Configuration Options

### Browser Settings

- `HEADLESS_MODE`: Run browser in headless mode
- `BROWSER_TIMEOUT`: Browser operation timeout
- `STEALTH_MODE`: Enable anti-detection features
- `RANDOM_USER_AGENTS`: Use random user agents
- `RANDOM_VIEWPORTS`: Use random viewport sizes

### Registration Settings

- `MAX_CONCURRENT_REGISTRATIONS`: Max parallel registrations
- `DELAY_BETWEEN_REGISTRATIONS`: Delay between registrations (seconds)
- `MAX_RETRY_ATTEMPTS`: Max retry attempts for failed operations

### Human Behavior

- `HUMAN_DELAYS`: Enable human-like delays
- `MIN_TYPING_DELAY`: Minimum typing delay
- `MAX_TYPING_DELAY`: Maximum typing delay
- `MISTAKE_RATE`: Typing mistake rate (0.0-1.0)

### US Localization

- Timezones: Eastern, Central, Mountain, Pacific, Arizona, Michigan
- Names: American first and last names
- Phone numbers: US format with valid area codes
- Localization: en-US locale and language settings

## 📊 Output Files

### Successful Accounts (`data/gmail_accounts.json`)

```json
[
  {
    "success": true,
    "email": "<EMAIL>",
    "user_data": {
      "first_name": "John",
      "last_name": "Smith",
      "username": "johnsmith123",
      "password": "SecurePass123!",
      "phone": "+***********",
      "timezone": "America/New_York"
    },
    "timestamp": "2024-01-15T10:30:45"
  }
]
```

### Batch Results (`data/batch_results_*.json`)

```json
[
  {
    "success": true,
    "account_id": 1,
    "email": "<EMAIL>",
    "duration_seconds": 45.2
  },
  {
    "success": false,
    "account_id": 2,
    "error": "Captcha failed",
    "duration_seconds": 30.1
  }
]
```

## 🛡️ Anti-Detection Features

### Browser Profile Randomization

- Random viewport sizes (1366x768, 1920x1080, etc.)
- Random user agents (Chrome-based)
- Random timezone selection
- Realistic screen resolutions

### Human Behavior Simulation

- Natural typing with random delays and mistakes
- Random mouse movements
- Reading pauses and thinking delays
- Realistic form filling patterns

### Stealth Techniques

- WebDriver property removal
- Plugin and media device simulation
- Language and timezone spoofing
- Chrome automation flags removal

## 🌐 Proxy Support

### Proxy Configuration

```python
# In config.py
PROXY_LIST = [
    ProxyConfig("ip:port", "username", "password"),
    # Add multiple proxies for rotation
]
```

### Proxy Features

- Automatic proxy rotation
- Health checking and validation
- Failed proxy blacklisting
- Response time monitoring
- Concurrent proxy testing

## 🚨 Safety Features

### Rate Limiting

- Configurable delays between registrations (default: 5 minutes)
- Maximum concurrent registrations (default: 2)
- Exponential backoff on failures

### Error Handling

- Comprehensive exception handling
- Graceful degradation on failures
- Detailed error logging
- Automatic retry mechanisms

### Legal Compliance

- Built-in warnings and confirmations
- Test mode for safe development
- Configuration validation
- Usage logging and monitoring

## 🧪 Testing

### Test Mode

```bash
# Run without actual registration
python main.py --test-mode --accounts 3
```

### Configuration Check

```bash
# Validate configuration
python main.py --config-check
```

### Proxy Testing

```bash
# Test all configured proxies
python main.py --proxy-test
```

### Individual Module Testing

```bash
# Test user data generation
python user_data_generator.py

# Test browser profile creation
python browser_profile.py

# Test proxy manager
python proxy_manager.py
```

## 📈 Performance & Scalability

### Recommended Settings

- **Small scale (1-5 accounts)**: 1 concurrent, 5-minute delays
- **Medium scale (5-20 accounts)**: 2 concurrent, 5-minute delays
- **Large scale (20+ accounts)**: 2-3 concurrent, 10-minute delays

### Resource Requirements

- RAM: 2GB+ (per concurrent browser)
- CPU: Multi-core recommended for concurrent operations
- Network: Stable connection, proxy bandwidth considerations
- Storage: 1GB+ for logs and data

## 🔍 Troubleshooting

### Common Issues

**No LLM API Key**

```bash
Error: No LLM API key provided
Solution: Set OPENAI_API_KEY or ANTHROPIC_API_KEY in .env
```

**Proxy Connection Failed**

```bash
Error: No working proxies found
Solution: Check proxy configuration and credentials
```

**Browser Launch Failed**

```bash
Error: Browser launch failed
Solution: Run `playwright install chromium`
```

**Captcha Blocked**

```bash
Error: Captcha failed
Solution: Reduce registration frequency, improve proxy quality
```

### Debug Mode

```bash
# Enable detailed logging
export LOG_LEVEL=DEBUG
python main.py --test-mode
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [Browser-Use](https://github.com/browser-use/browser-use) - AI browser automation
- [Playwright](https://playwright.dev/) - Browser automation
- [Fake UserAgent](https://github.com/fake-useragent/fake-useragent) - User agent generation

## 📞 Support

For questions, issues, or contributions:

- Open an issue on GitHub
- Review the plan.md for detailed technical information
- Check the troubleshooting section above

---

**Remember: Use this tool responsibly and in compliance with all applicable laws and terms of service.**
