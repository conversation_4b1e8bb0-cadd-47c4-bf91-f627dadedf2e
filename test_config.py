"""
Test Configuration Override
Allows testing the system without API keys and proxies for demonstration purposes.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Override config for testing
os.environ['OPENAI_API_KEY'] = 'test-key-for-demo'
os.environ['USE_PROXY'] = 'false'

# Import after setting environment
from config import Config

# Override validation for testing
original_validate = Config.validate_config

@classmethod
def test_validate_config(cls) -> bool:
    """Test validation that bypasses API key requirements"""
    print("🧪 Using test configuration - bypassing API key validation")
    return True

# Replace validation method
Config.validate_config = test_validate_config

print("✅ Test configuration loaded - ready for testing")
print("   🚫 Proxy disabled")
print("   🧪 Mock API key set")
print("   📝 All validations bypassed") 