"""
Browser Profile Module
Creates human-like browser profiles with anti-detection features for Gmail registration.
"""

import random
from typing import Dict, Any, Optional, List
from fake_useragent import UserAgent
from config import Config, ProxyConfig
from constants import Browser, Localization, Headers, Timeouts, AntiDetection


class BrowserProfileGenerator:
    """Tạo browser profile giống người thật với anti-detection"""
    
    def __init__(self):
        self.ua = UserAgent()
        
    def create_human_browser_profile(self, proxy_config: Optional[ProxyConfig] = None) -> Dict[str, Any]:
        """
        Tạo browser profile hoàn chỉnh giống người thật
        
        Args:
            proxy_config: Optional proxy configuration
            
        Returns:
            Dict chứa browser profile configuration
        """
        # Random viewport size
        viewport = self._get_random_viewport()
        
        # Random timezone 
        timezone = random.choice(Localization.US_TIMEZONES)
        
        # Random user agent
        user_agent = self._get_random_user_agent()
        
        # Random screen resolution (larger than viewport)
        screen_width = viewport['width'] + random.randint(0, 200)
        screen_height = viewport['height'] + random.randint(0, 200)
        
        profile = {
            # Basic browser settings
            "headless": False,
            "user_agent": user_agent,
            "viewport": viewport,
            "locale": Localization.LOCALE,
            "timezone_id": timezone,
            
            # Performance settings
            "slow_mo": random.uniform(100, 300) if Config.HUMAN_DELAYS else 0,
            "timeout": Timeouts.BROWSER_DEFAULT * 1000,  # Convert to milliseconds
            
            # Anti-detection features
            "java_script_enabled": True,
            "stealth": Config.STEALTH_MODE,
            "ignore_https_errors": True,
            "ignore_default_args": ["--enable-automation"],
            
            # Chrome arguments
            "args": self._get_chrome_args(user_agent),
            
            # Screen and device settings
            "screen": {
                "width": screen_width,
                "height": screen_height
            },
            "device_scale_factor": random.choice(Browser.DEVICE_SCALE_FACTORS),
            
            # Proxy configuration
            "proxy": proxy_config.to_dict() if proxy_config else None,
            
            # Storage settings
            "user_data_dir": None,  # New profile each time
            "storage_state": None,  # No saved cookies
            
            # Debugging settings
            "record_video": False,
            "record_har": Config.SAVE_CONVERSATIONS,
            "extra_http_headers": self._get_realistic_headers(user_agent)
        }
        
        return profile
    
    def _get_random_viewport(self) -> Dict[str, int]:
        """Lấy viewport size ngẫu nhiên realistic"""
        if Config.RANDOM_VIEWPORTS:
            return random.choice(Browser.VIEWPORT_SIZES)
        else:
            return Browser.DEFAULT_VIEWPORT
    
    def _get_random_user_agent(self) -> str:
        """Lấy user agent ngẫu nhiên realistic"""
        if Config.RANDOM_USER_AGENTS:
            try:
                # Get Chrome user agent specifically
                return self.ua.chrome
            except:
                # Fallback to default
                return Browser.FALLBACK_USER_AGENT
        else:
            return Browser.FALLBACK_USER_AGENT
    
    def _get_chrome_args(self, user_agent: str) -> List[str]:
        """Tạo Chrome arguments để anti-detection"""
        base_args = Browser.CHROME_ARGS.copy()
        
        # Add user agent
        base_args.append(f'--user-agent={user_agent}')
        
        # Random window size
        viewport = self._get_random_viewport()
        base_args.append(f'--window-size={viewport["width"]},{viewport["height"]}')
        
        # Random memory settings
        memory_settings = Browser.MEMORY_SETTINGS
        max_old_space = random.randint(memory_settings["max_old_space_size"]["min"], 
                                     memory_settings["max_old_space_size"]["max"])
        max_heap = random.randint(memory_settings["max_heap_size"]["min"], 
                                memory_settings["max_heap_size"]["max"])
        
        base_args.extend([
            f'--max_old_space_size={max_old_space}',
            f'--max-heap-size={max_heap}'
        ])
        
        # Language settings
        base_args.extend([
            f'--lang={Localization.LOCALE}',
            f'--accept-lang={Localization.LANGUAGES["accept_language"]}'
        ])
        
        return base_args
    
    def _get_realistic_headers(self, user_agent: str) -> Dict[str, str]:
        """Tạo HTTP headers realistic"""
        headers = Headers.REALISTIC_HEADERS.copy()
        
        # Override user agent
        headers['User-Agent'] = user_agent
        
        # Dynamic chrome version for sec-ch-ua
        chrome_ver_range = Headers.CHROME_VERSION_RANGE
        chrome_version = random.randint(chrome_ver_range["min"], chrome_ver_range["max"])
        headers['sec-ch-ua'] = f'"Google Chrome";v="{chrome_version}", "Chromium";v="{chrome_version}", "Not?A_Brand";v="99"'
        
        return headers


class AntiDetectionManager:
    """Quản lý các kỹ thuật chống phát hiện bot"""
    
    @staticmethod
    def get_stealth_scripts() -> List[str]:
        """
        Lấy JavaScript scripts để stealth mode
        
        Returns:
            List các JavaScript code để inject
        """
        return list(AntiDetection.STEALTH_SCRIPTS.values())
        
        # Override permissions
        scripts.append("""
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        """)
        
        # Mock media devices
        scripts.append("""
            Object.defineProperty(navigator, 'mediaDevices', {
                get: () => ({
                    enumerateDevices: () => Promise.resolve([
                        {
                            deviceId: 'default',
                            kind: 'audioinput',
                            label: 'Default - Microphone Array (Realtek High Definition Audio)',
                            groupId: 'default'
                        },
                        {
                            deviceId: 'communications',
                            kind: 'audioinput', 
                            label: 'Communications - Microphone Array (Realtek High Definition Audio)',
                            groupId: 'communications'
                        }
                    ])
                })
            });
        """)
        
        # Override chrome object
        scripts.append("""
            if (!window.chrome) {
                window.chrome = {};
            }
            
            if (!window.chrome.runtime) {
                window.chrome.runtime = {
                    onConnect: {
                        addListener: function() {},
                        removeListener: function() {}
                    },
                    onMessage: {
                        addListener: function() {},
                        removeListener: function() {}
                    }
                };
            }
        """)
        
        # Random screen properties
        viewport = random.choice(Config.VIEWPORT_SIZES)
        screen_width = viewport['width'] + random.randint(0, 200)
        screen_height = viewport['height'] + random.randint(0, 200)
        
        scripts.append(f"""
            Object.defineProperty(screen, 'width', {{
                get: () => {screen_width}
            }});
            Object.defineProperty(screen, 'height', {{
                get: () => {screen_height}
            }});
            Object.defineProperty(screen, 'availWidth', {{
                get: () => {screen_width}
            }});
            Object.defineProperty(screen, 'availHeight', {{
                get: () => {screen_height - 40}
            }});
        """)
        
        return scripts
    
    @staticmethod
    def get_mouse_movement_script() -> str:
        """Script để random mouse movement"""
        return """
            // Random mouse movement
            function randomMouseMove() {
                const x = Math.random() * window.innerWidth;
                const y = Math.random() * window.innerHeight;
                
                const event = new MouseEvent('mousemove', {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    clientX: x,
                    clientY: y
                });
                
                document.dispatchEvent(event);
            }
            
            // Random movement every 5-15 seconds
            setInterval(randomMouseMove, Math.random() * 10000 + 5000);
        """
    
    @staticmethod
    def get_timezone_script(timezone: str) -> str:
        """Script để set timezone"""
        return f"""
            // Override timezone
            const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
            Date.prototype.getTimezoneOffset = function() {{
                return new Intl.DateTimeFormat('en', {{
                    timeZone: '{timezone}',
                    timeZoneName: 'short'
                }}).formatToParts(new Date()).find(part => part.type === 'timeZoneName').value;
            }};
            
            // Override Intl.DateTimeFormat
            const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
            Intl.DateTimeFormat.prototype.resolvedOptions = function() {{
                const options = originalResolvedOptions.call(this);
                options.timeZone = '{timezone}';
                return options;
            }};
        """


# Utility functions
def create_human_profile(proxy_config: Optional[ProxyConfig] = None) -> Dict[str, Any]:
    """
    Convenience function để tạo human browser profile
    
    Args:
        proxy_config: Optional proxy configuration
        
    Returns:
        Browser profile dict
    """
    generator = BrowserProfileGenerator()
    return generator.create_human_browser_profile(proxy_config)


def get_stealth_scripts() -> List[str]:
    """
    Convenience function để lấy stealth scripts
    
    Returns:
        List of JavaScript scripts
    """
    return AntiDetectionManager.get_stealth_scripts()


# For testing
if __name__ == "__main__":
    print("🧪 Testing Browser Profile Generator...")
    
    # Test profile generation
    generator = BrowserProfileGenerator()
    
    # Test without proxy
    print("\n1. Generating profile without proxy:")
    profile1 = generator.create_human_browser_profile()
    print(f"   User Agent: {profile1['user_agent'][:50]}...")
    print(f"   Viewport: {profile1['viewport']}")
    print(f"   Timezone: {profile1['timezone_id']}")
    print(f"   Stealth: {profile1['stealth']}")
    print(f"   Chrome Args: {len(profile1['args'])} arguments")
    
    # Test with proxy
    print("\n2. Generating profile with proxy:")
    from config import ProxyConfig
    test_proxy = ProxyConfig("proxy.example.com:8080", "user", "pass")
    profile2 = generator.create_human_browser_profile(test_proxy)
    print(f"   Proxy: {profile2['proxy']}")
    print(f"   Different UA: {profile1['user_agent'] != profile2['user_agent']}")
    
    # Test stealth scripts
    print("\n3. Testing stealth scripts:")
    scripts = AntiDetectionManager.get_stealth_scripts()
    print(f"   Generated {len(scripts)} stealth scripts")
    
    # Print sample script
    if scripts:
        print(f"   Sample script: {scripts[0][:100]}...")
    
    print("\n✅ Browser Profile Generator tests completed") 