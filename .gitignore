# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Local development
.env
.env.local
.env.*.local

# Testing
.coverage
htmlcov/
.pytest_cache/

# Project specific
screenshots/*
!screenshots/.gitkeep
data/*
!data/.gitkeep
conversations/*
!conversations/.gitkeep

# Keep these directories
!web-bundles/
!.bmad-core/ 