# 📋 Plan Project: Gmail Auto Registration với Browser-Use

## ⚠️ Cảnh báo quan trọng

Trước khi thực hiện project này, cần lưu ý rằng việc đăng ký tài khoản Gmail tự động có thể:

- Vi phạm Terms of Service của Google
- C<PERSON> thể bị coi là spam hoặc lạm dụng hệ thống
- C<PERSON> thể dẫn đến việc tài khoản bị khóa

**Khuyến nghị**: Chỉ sử dụng cho mục đích testing và tuân thủ các quy định pháp luật.

---

## 🎯 Mục tiêu Project

Tạo một hệ thống đăng ký Gmail tự động sử dụng Browser-Use với:

- ✅ Hành vi giống người dùng thật (human-like behavior)
- ✅ Sử dụng proxy rotation
- ✅ Anti-detection techniques
- ✅ Chrome browser automation
- ✅ Random delays và mouse movements

---

## 🚀 Phase 1: Chuẩn bị và Cài đặt

### Dependencies c<PERSON><PERSON> thiết:

```bash
# Core dependencies
pip install browser-use playwright
pip install fake-useragent
pip install python-anticaptcha
pip install proxy-requests
pip install asyncio-throttle

# Optional but recommended
pip install selenium-stealth
pip install undetected-chromedriver
```

### Environment Setup:

```bash
# Tạo .env file
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here

# Proxy settings
PROXY_ROTATION_ENABLED=true
CAPTCHA_SERVICE_KEY=your_captcha_service_key

# Logging
BROWSER_USE_LOGGING_LEVEL=debug
```

---

## 🎭 Phase 2: Human-like Browser Profile

### Core Browser Configuration:

```python
from browser_use import BrowserSession, BrowserProfile, Agent
from fake_useragent import UserAgent
import random
import time

def create_human_browser_profile(proxy_config=None):
    """Tạo browser profile giống người thật"""
    ua = UserAgent()

    # Random viewport sizes phổ biến
    viewports = [
        {"width": 1366, "height": 768},   # Laptop phổ biến
        {"width": 1920, "height": 1080},  # Desktop HD
        {"width": 1440, "height": 900},   # MacBook
        {"width": 1536, "height": 864},   # Surface
        {"width": 1280, "height": 720}    # Smaller laptop
    ]

        # Random timezone (Mỹ và khu vực)
    timezones = [
        'America/New_York',     # Eastern Time
        'America/Chicago',      # Central Time
        'America/Denver',       # Mountain Time
        'America/Los_Angeles',  # Pacific Time
        'America/Phoenix'       # Arizona Time
        'America/Michigan',     # Detroit Time
    ]

    browser_profile = BrowserProfile(
        # Basic settings
        headless=False,  # Hiển thị browser để giống human
        user_agent=ua.random,
        viewport=random.choice(viewports),
        locale='en-US',
        timezone_id=random.choice(timezones),

        # Human-like behavior
        wait_for_network_idle_page_load_time=random.uniform(2.0, 4.5),
        slow_mo=random.uniform(100, 300),  # Delay giữa các action

        # Anti-detection features
        stealth=True,
        highlight_elements=False,  # Tắt highlight
        disable_web_security=False,

        # Proxy configuration
        proxy=proxy_config if proxy_config else None,

        # Advanced Chrome args để tránh detection
        chromium_args=[
            '--disable-blink-features=AutomationControlled',
            '--exclude-switches=enable-automation',
            '--disable-extensions-file-access-check',
            '--disable-extensions-http-throttling',
            '--disable-extensions-https-throttling',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI',
            '--disable-component-extensions-with-background-pages',
            f'--user-agent={ua.random}'
        ],

        # Storage and cookies
        user_data_dir=None,  # Mỗi session có thư mục riêng
        storage_state=None,  # Không load cookies cũ

        # Performance settings
        record_video=False,
        record_har=False
    )

    return browser_profile
```

---

## 🤖 Phase 3: Human Behavior Simulation

### Human-like Actions Class:

```python
import asyncio
import random
from typing import Dict, Any
from playwright.async_api import Page

class HumanBehavior:
    """Mô phỏng hành vi người dùng thật"""

    @staticmethod
    async def human_delay(min_delay=1, max_delay=3):
        """Random delay giống human với phân phối thực tế"""
        # Sử dụng phân phối log-normal để giống human hơn
        delay = random.lognormvariate(
            mu=(min_delay + max_delay) / 2,
            sigma=0.5
        )
        delay = max(min_delay, min(delay, max_delay))
        await asyncio.sleep(delay)

    @staticmethod
    async def human_typing(page: Page, selector: str, text: str, mistake_rate=0.05):
        """Gõ chậm giống human với khả năng gõ sai"""
        await page.click(selector)
        await HumanBehavior.human_delay(0.3, 0.8)

        for i, char in enumerate(text):
            # Thỉnh thoảng gõ sai và sửa lại
            if random.random() < mistake_rate:
                wrong_char = random.choice('abcdefghijklmnopqrstuvwxyz')
                await page.keyboard.type(wrong_char)
                await asyncio.sleep(random.uniform(0.1, 0.3))
                await page.keyboard.press('Backspace')
                await asyncio.sleep(random.uniform(0.1, 0.2))

            await page.keyboard.type(char)
            # Delay ngẫu nhiên giữa các ký tự
            await asyncio.sleep(random.uniform(0.05, 0.25))

            # Pause dài hơn sau dấu cách hoặc dấu chấm
            if char in [' ', '.', ',']:
                await asyncio.sleep(random.uniform(0.2, 0.5))

    @staticmethod
    async def random_mouse_movement(page: Page, num_movements=3):
        """Di chuyển chuột ngẫu nhiên như human"""
        viewport = await page.viewport_size()

        for _ in range(num_movements):
            # Tránh các góc màn hình
            x = random.randint(50, viewport['width'] - 50)
            y = random.randint(50, viewport['height'] - 50)

            await page.mouse.move(x, y)
            await asyncio.sleep(random.uniform(0.3, 1.0))

    @staticmethod
    async def random_scroll(page: Page):
        """Cuộn trang ngẫu nhiên như human đọc"""
        scroll_directions = ['up', 'down']
        direction = random.choice(scroll_directions)

        if direction == 'down':
            await page.mouse.wheel(0, random.randint(100, 300))
        else:
            await page.mouse.wheel(0, -random.randint(100, 300))

        await HumanBehavior.human_delay(0.5, 2.0)

    @staticmethod
    async def reading_pause(page: Page, element_selector=None):
        """Dừng lại để 'đọc' như human"""
        if element_selector:
            try:
                await page.hover(element_selector)
            except:
                pass

        # Thời gian đọc ngẫu nhiên
        await asyncio.sleep(random.uniform(2.0, 5.0))
```

---

## 👤 Phase 4: User Data Generation

### Realistic User Data Generator:

```python
import string
import random
from datetime import datetime, timedelta

class UserDataGenerator:
    """Tạo dữ liệu user thực tế cho đăng ký"""

         # Data pools để tạo thông tin thực tế
     AMERICAN_FIRST_NAMES = [
         "James", "Mary", "John", "Patricia", "Robert", "Jennifer", "Michael", "Linda",
         "William", "Elizabeth", "David", "Barbara", "Richard", "Susan", "Joseph", "Jessica"
     ]

     AMERICAN_LAST_NAMES = [
         "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
         "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas"
     ]

    COMMON_DOMAINS = [
        "gmail.com", "outlook.com", "yahoo.com", "hotmail.com"
    ]

    @classmethod
    def generate_user_info(cls) -> Dict[str, Any]:
                 """Tạo thông tin user hoàn chỉnh"""
         first_name = random.choice(cls.AMERICAN_FIRST_NAMES)
         last_name = random.choice(cls.AMERICAN_LAST_NAMES)

        # Tạo username realistic
        username_patterns = [
            f"{first_name.lower()}{last_name.lower()}{random.randint(1990, 2005)}",
            f"{first_name.lower()}_{last_name.lower()}_{random.randint(10, 99)}",
            f"{last_name.lower()}{first_name.lower()}{random.randint(100, 999)}",
            f"{first_name.lower()}.{last_name.lower()}{random.randint(1, 99)}"
        ]

        # Tạo password mạnh
        password = cls._generate_strong_password()

        # Tạo ngày sinh realistic (18-35 tuổi)
        birth_year = random.randint(1988, 2005)
        birth_month = random.randint(1, 12)
        birth_day = random.randint(1, 28)  # Tránh lỗi ngày không tồn tại

                 # Phone number Mỹ
         area_codes = ['202', '212', '213', '214', '305', '310', '312', '323', '415', '510', '602', '702', '713', '714', '718', '773', '818', '917']
         phone = f"+1{random.choice(area_codes)}{random.randint(1000000, 9999999)}"

        return {
            "first_name": first_name,
            "last_name": last_name,
            "username": random.choice(username_patterns),
            "password": password,
            "birth_year": birth_year,
            "birth_month": birth_month,
            "birth_day": birth_day,
            "phone": phone,
            "recovery_email": f"{random.choice(username_patterns)}@{random.choice(cls.COMMON_DOMAINS)}",
            "gender": random.choice(["male", "female", "prefer_not_to_say"])
        }

    @classmethod
    def _generate_strong_password(cls, length=12) -> str:
        """Tạo password mạnh"""
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(random.choices(chars, k=length))

        # Đảm bảo có ít nhất 1 chữ hoa, 1 chữ thường, 1 số, 1 ký tự đặc biệt
        if not any(c.isupper() for c in password):
            password = password[:-1] + random.choice(string.ascii_uppercase)
        if not any(c.islower() for c in password):
            password = password[:-1] + random.choice(string.ascii_lowercase)
        if not any(c.isdigit() for c in password):
            password = password[:-1] + random.choice(string.digits)
        if not any(c in "!@#$%^&*" for c in password):
            password = password[:-1] + random.choice("!@#$%^&*")

        return password
```

---

## 🌐 Phase 5: Proxy Management

### Proxy Rotation System:

```python
import aiohttp
import asyncio
from typing import List, Dict, Optional

class ProxyRotator:
    """Quản lý và rotation proxy"""

    def __init__(self, proxy_list: List[Dict]):
        self.proxies = proxy_list
        self.current_index = 0
        self.failed_proxies = set()

    def get_next_proxy(self) -> Optional[Dict]:
        """Lấy proxy tiếp theo trong rotation"""
        attempts = 0
        while attempts < len(self.proxies):
            proxy = self.proxies[self.current_index]
            self.current_index = (self.current_index + 1) % len(self.proxies)

            if proxy.get('server') not in self.failed_proxies:
                return proxy

            attempts += 1

        return None  # Tất cả proxy đều failed

    async def test_proxy(self, proxy: Dict) -> bool:
        """Test proxy có hoạt động không"""
        try:
            proxy_url = f"http://{proxy['username']}:{proxy['password']}@{proxy['server']}"

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    'http://httpbin.org/ip',
                    proxy=proxy_url,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        return True
        except:
            pass

        return False

    def mark_proxy_failed(self, proxy: Dict):
        """Đánh dấu proxy failed"""
        self.failed_proxies.add(proxy.get('server'))

# Proxy configuration examples
PROXY_POOLS = [
    {
        "server": "proxy1.example.com:8080",
        "username": "user1",
        "password": "pass1"
    },
    {
        "server": "proxy2.example.com:8080",
        "username": "user2",
        "password": "pass2"
    },
    # Thêm nhiều proxy khác...
]
```

---

## 🎯 Phase 6: Main Gmail Registration Agent

### Core Registration Logic:

```python
from browser_use import Agent, BrowserSession
from browser_use.llm import ChatOpenAI
import asyncio
import json
from pathlib import Path

class GmailRegistrationAgent:
    """Agent chính để đăng ký Gmail tự động"""

    def __init__(self, proxy_config=None, llm_model="gpt-4o-mini"):
        self.proxy_config = proxy_config
        self.user_data = UserDataGenerator.generate_user_info()
        self.llm = ChatOpenAI(model=llm_model)
        self.success_log = []
        self.error_log = []

    async def register_gmail(self) -> Dict[str, Any]:
        """Đăng ký Gmail chính"""
        browser_profile = create_human_browser_profile(self.proxy_config)

        browser_session = BrowserSession(
            browser_profile=browser_profile,
            keep_alive=True
        )

        try:
            await browser_session.start()
            page = await browser_session.get_current_page()

            # Log bắt đầu quá trình
            print(f"🚀 Bắt đầu đăng ký Gmail cho user: {self.user_data['username']}")

            # Step 1: Điều hướng và chuẩn bị
            success = await self._navigate_to_signup(page)
            if not success:
                return {"success": False, "error": "Navigation failed"}

            # Step 2: Điền form đăng ký
            success = await self._fill_registration_form(page)
            if not success:
                return {"success": False, "error": "Form filling failed"}

            # Step 3: Xử lý captcha
            success = await self._handle_captcha(page)
            if not success:
                return {"success": False, "error": "Captcha failed"}

            # Step 4: Xử lý xác thực
            success = await self._handle_verification(page)
            if not success:
                return {"success": False, "error": "Verification failed"}

            # Success!
            result = {
                "success": True,
                "user_data": self.user_data,
                "email": f"{self.user_data['username']}@gmail.com",
                "timestamp": datetime.now().isoformat()
            }

            self.success_log.append(result)
            await self._save_success_data(result)

            return result

        except Exception as e:
            error_data = {
                "error": str(e),
                "user_data": self.user_data,
                "timestamp": datetime.now().isoformat()
            }
            self.error_log.append(error_data)
            print(f"❌ Lỗi đăng ký: {e}")
            return {"success": False, "error": str(e)}

        finally:
            await browser_session.close()

    async def _navigate_to_signup(self, page) -> bool:
        """Điều hướng đến trang đăng ký"""
        try:
            print("📍 Điều hướng đến trang đăng ký Gmail...")

            # Random mouse movement trước khi bắt đầu
            await HumanBehavior.random_mouse_movement(page)

            # Điều hướng đến trang đăng ký
            await page.goto('https://accounts.google.com/signup/v2/createaccount?flowName=GlifWebSignIn&flowEntry=SignUp')

            # Đợi trang load và đọc một chút
            await HumanBehavior.human_delay(3, 6)
            await HumanBehavior.reading_pause(page)

            # Kiểm tra có trang đăng ký không
            signup_form = await page.query_selector('form')
            if not signup_form:
                print("❌ Không tìm thấy form đăng ký")
                return False

            print("✅ Đã load trang đăng ký thành công")
            return True

        except Exception as e:
            print(f"❌ Lỗi điều hướng: {e}")
            return False

    async def _fill_registration_form(self, page) -> bool:
        """Điền form đăng ký với AI agent"""
        try:
            print("📝 Bắt đầu điền form đăng ký...")

            # Sử dụng Browser-Use Agent để điền form thông minh
            agent = Agent(
                task=f"""
                Điền form đăng ký Gmail với thông tin sau:
                - Họ: {self.user_data['first_name']}
                - Tên: {self.user_data['last_name']}
                - Tên đăng nhập: {self.user_data['username']}
                - Mật khẩu: {self.user_data['password']}
                - Ngày sinh: {self.user_data['birth_day']}/{self.user_data['birth_month']}/{self.user_data['birth_year']}

                Hãy điền các field một cách tự nhiên, có delay giữa các action.
                Nếu có dropdown cho ngày sinh, hãy chọn đúng ngày/tháng/năm.
                Sau khi điền xong, click nút tiếp theo.
                """,
                llm=self.llm,
                page=page,
                use_vision=True
            )

            # Chạy agent với hook để theo dõi
            history = await agent.run(max_steps=15)

            # Kiểm tra kết quả
            if any("success" in str(step).lower() for step in history.history):
                print("✅ Điền form thành công")
                return True
            else:
                print("❌ Điền form thất bại")
                return False

        except Exception as e:
            print(f"❌ Lỗi điền form: {e}")
            return False

    async def _handle_captcha(self, page) -> bool:
        """Xử lý captcha nếu xuất hiện"""
        try:
            print("🔍 Kiểm tra captcha...")

            # Đợi một chút để captcha load
            await HumanBehavior.human_delay(2, 4)

            # Kiểm tra có captcha không
            captcha_selectors = [
                '[data-sitekey]',  # reCAPTCHA
                '.recaptcha-checkbox',
                '#recaptcha',
                'iframe[src*="recaptcha"]'
            ]

            captcha_found = False
            for selector in captcha_selectors:
                element = await page.query_selector(selector)
                if element:
                    captcha_found = True
                    break

            if not captcha_found:
                print("✅ Không có captcha")
                return True

            print("🤖 Phát hiện captcha, sử dụng AI để giải...")

            # Sử dụng Browser-Use Agent để giải captcha
            agent = Agent(
                task="""
                Có captcha trên trang này. Hãy:
                1. Kiểm tra loại captcha (reCAPTCHA, hCaptcha, etc.)
                2. Nếu là checkbox reCAPTCHA đơn giản, hãy click vào checkbox
                3. Nếu là captcha hình ảnh, hãy phân tích và chọn đúng hình
                4. Sau khi hoàn thành, click nút tiếp theo

                Hãy thực hiện cẩn thận và chờ đợi response từ captcha.
                """,
                llm=self.llm,
                page=page,
                use_vision=True
            )

            history = await agent.run(max_steps=10)

            # Đợi một chút sau khi giải captcha
            await HumanBehavior.human_delay(2, 4)

            print("✅ Đã xử lý captcha")
            return True

        except Exception as e:
            print(f"❌ Lỗi xử lý captcha: {e}")
            return False

    async def _handle_verification(self, page) -> bool:
        """Xử lý bước xác thực phone/email"""
        try:
            print("📱 Xử lý bước xác thực...")

            # Đợi trang load
            await HumanBehavior.human_delay(3, 5)

            # Sử dụng AI agent để xử lý verification
            agent = Agent(
                task=f"""
                Đây là bước xác thực tài khoản Gmail. Hãy:

                1. Kiểm tra trang hiện tại có yêu cầu gì:
                   - Xác thực số điện thoại
                   - Email khôi phục
                   - Bỏ qua verification

                2. Nếu có tùy chọn "Skip" hoặc "Do this later", hãy chọn nó

                3. Nếu bắt buộc phải nhập phone, hãy nhập: {self.user_data['phone']}

                4. Nếu yêu cầu email khôi phục, hãy nhập: {self.user_data['recovery_email']}

                5. Click tiếp theo để hoàn thành

                Ưu tiên skip verification nếu có thể.
                """,
                llm=self.llm,
                page=page,
                use_vision=True
            )

            history = await agent.run(max_steps=10)

            # Đợi hoàn thành
            await HumanBehavior.human_delay(3, 5)

            print("✅ Đã xử lý verification")
            return True

        except Exception as e:
            print(f"❌ Lỗi xử lý verification: {e}")
            return False

    async def _save_success_data(self, result):
        """Lưu dữ liệu thành công"""
        success_file = Path("gmail_accounts.json")

        if success_file.exists():
            with open(success_file, 'r', encoding='utf-8') as f:
                accounts = json.load(f)
        else:
            accounts = []

        accounts.append(result)

        with open(success_file, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, indent=2, ensure_ascii=False)
```

---

## 🔄 Phase 7: Main Execution & Orchestration

### Batch Registration System:

```python
import asyncio
import random
from datetime import datetime, timedelta

class GmailBatchRegistration:
    """Hệ thống đăng ký Gmail hàng loạt"""

    def __init__(self, proxy_list, max_concurrent=2):
        self.proxy_rotator = ProxyRotator(proxy_list)
        self.max_concurrent = max_concurrent
        self.results = []
        self.semaphore = asyncio.Semaphore(max_concurrent)

    async def register_single_account(self, account_id: int):
        """Đăng ký một tài khoản"""
        async with self.semaphore:
            try:
                print(f"🎯 Bắt đầu đăng ký tài khoản #{account_id}")

                # Lấy proxy tiếp theo
                proxy = self.proxy_rotator.get_next_proxy()
                if not proxy:
                    print("❌ Không còn proxy khả dụng")
                    return {"success": False, "error": "No proxy available"}

                # Test proxy trước khi sử dụng
                if not await self.proxy_rotator.test_proxy(proxy):
                    self.proxy_rotator.mark_proxy_failed(proxy)
                    print(f"❌ Proxy {proxy['server']} failed test")
                    return {"success": False, "error": "Proxy failed"}

                # Tạo agent và đăng ký
                agent = GmailRegistrationAgent(proxy_config=proxy)
                result = await agent.register_gmail()

                # Log kết quả
                if result["success"]:
                    print(f"✅ Tài khoản #{account_id} đăng ký thành công: {result['email']}")
                else:
                    print(f"❌ Tài khoản #{account_id} thất bại: {result['error']}")

                return result

            except Exception as e:
                print(f"❌ Lỗi đăng ký tài khoản #{account_id}: {e}")
                return {"success": False, "error": str(e)}

            finally:
                # Delay giữa các lần đăng ký
                delay = random.uniform(300, 600)  # 5-10 phút
                print(f"⏰ Đợi {delay/60:.1f} phút trước lần đăng ký tiếp theo...")
                await asyncio.sleep(delay)

    async def run_batch_registration(self, num_accounts: int):
        """Chạy đăng ký hàng loạt"""
        print(f"🚀 Bắt đầu đăng ký {num_accounts} tài khoản Gmail")
        print(f"⚙️ Chạy tối đa {self.max_concurrent} tài khoản đồng thời")

        # Tạo tasks cho tất cả accounts
        tasks = []
        for i in range(num_accounts):
            task = self.register_single_account(i + 1)
            tasks.append(task)

        # Chạy tất cả tasks
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Tổng hợp kết quả
        successful = sum(1 for r in results if isinstance(r, dict) and r.get("success"))
        failed = len(results) - successful

        print(f"\n📊 Kết quả tổng quan:")
        print(f"✅ Thành công: {successful}/{num_accounts}")
        print(f"❌ Thất bại: {failed}/{num_accounts}")
        print(f"📈 Tỷ lệ thành công: {successful/num_accounts*100:.1f}%")

        return results

# Main execution
async def main():
    """Entry point chính"""

    # Cấu hình proxy list
    proxy_list = [
        {
            "server": "proxy1.example.com:8080",
            "username": "user1",
            "password": "pass1"
        },
        {
            "server": "proxy2.example.com:8080",
            "username": "user2",
            "password": "pass2"
        },
        # Thêm nhiều proxy...
    ]

    # Tạo batch registration system
    batch_system = GmailBatchRegistration(
        proxy_list=proxy_list,
        max_concurrent=1  # Bắt đầu với 1 để test
    )

    # Chạy đăng ký 5 tài khoản
    results = await batch_system.run_batch_registration(num_accounts=5)

    # Save results
    with open(f"batch_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", 'w') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)

if __name__ == "__main__":
    asyncio.run(main())
```

---

## 🛡️ Phase 8: Anti-Detection & Security

### Advanced Anti-Detection Techniques:

```python
class AntiDetectionManager:
    """Quản lý các kỹ thuật chống phát hiện bot"""

    @staticmethod
    async def setup_stealth_mode(page):
        """Setup stealth mode cho page"""
        # Loại bỏ các dấu hiệu của automation
        await page.add_init_script("""
            // Xóa webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // Fake plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });

                         // Fake languages
             Object.defineProperty(navigator, 'languages', {
                 get: () => ['en-US', 'en', 'es-US', 'es'],
             });

            // Override permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        """)

    @staticmethod
    def generate_realistic_headers():
        """Tạo headers giống người dùng thật"""
        ua = UserAgent()
                 return {
             'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
             'Accept-Language': 'en-US,en;q=0.9,es-US;q=0.8,es;q=0.7',
             'Accept-Encoding': 'gzip, deflate, br',
             'DNT': '1',
             'Connection': 'keep-alive',
             'Upgrade-Insecure-Requests': '1',
             'User-Agent': ua.random
         }
```

---

## 📊 Phase 9: Monitoring & Analytics

### Success Tracking System:

```python
import json
from pathlib import Path
from datetime import datetime
import sqlite3

class RegistrationAnalytics:
    """Theo dõi và phân tích kết quả đăng ký"""

    def __init__(self, db_path="gmail_analytics.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """Khởi tạo database tracking"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS registrations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE,
                username TEXT,
                password TEXT,
                proxy_used TEXT,
                success BOOLEAN,
                error_message TEXT,
                timestamp DATETIME,
                registration_time_seconds FLOAT
            )
        """)

        conn.commit()
        conn.close()

    def log_registration(self, result: Dict):
        """Log kết quả đăng ký"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO registrations
            (email, username, password, proxy_used, success, error_message, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            result.get('email'),
            result.get('username'),
            result.get('password'),
            result.get('proxy_server'),
            result.get('success'),
            result.get('error'),
            datetime.now().isoformat()
        ))

        conn.commit()
        conn.close()

    def get_success_rate(self) -> float:
        """Tính tỷ lệ thành công"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT COUNT(*) FROM registrations WHERE success = 1")
        success_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM registrations")
        total_count = cursor.fetchone()[0]

        conn.close()

        if total_count == 0:
            return 0.0

        return (success_count / total_count) * 100
```

---

## 🔧 Phase 10: Configuration & Deployment

### Complete Configuration File:

```python
# config.py
import os
from pathlib import Path

class Config:
    """Cấu hình tổng thể cho project"""

    # API Keys
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')

    # Browser settings
    HEADLESS_MODE = os.getenv('HEADLESS_MODE', 'false').lower() == 'true'
    BROWSER_TIMEOUT = int(os.getenv('BROWSER_TIMEOUT', '30'))

    # Proxy settings
    USE_PROXY = os.getenv('USE_PROXY', 'true').lower() == 'true'
    PROXY_ROTATION = os.getenv('PROXY_ROTATION', 'true').lower() == 'true'

    # Registration settings
    MAX_CONCURRENT_REGISTRATIONS = int(os.getenv('MAX_CONCURRENT', '2'))
    DELAY_BETWEEN_REGISTRATIONS = int(os.getenv('DELAY_BETWEEN', '300'))

    # Captcha settings
    CAPTCHA_SERVICE = os.getenv('CAPTCHA_SERVICE', 'none')
    CAPTCHA_API_KEY = os.getenv('CAPTCHA_API_KEY')

    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    SAVE_SCREENSHOTS = os.getenv('SAVE_SCREENSHOTS', 'true').lower() == 'true'

    # Data paths
    DATA_DIR = Path('data')
    LOGS_DIR = Path('logs')
    SCREENSHOTS_DIR = Path('screenshots')

    @classmethod
    def ensure_directories(cls):
        """Tạo các thư mục cần thiết"""
        cls.DATA_DIR.mkdir(exist_ok=True)
        cls.LOGS_DIR.mkdir(exist_ok=True)
        cls.SCREENSHOTS_DIR.mkdir(exist_ok=True)

# requirements.txt
REQUIREMENTS = """
browser-use>=0.1.0
playwright>=1.40.0
fake-useragent>=1.4.0
python-anticaptcha>=0.7.1
aiohttp>=3.9.0
asyncio-throttle>=1.0.2
python-dotenv>=1.0.0
pydantic>=2.0.0
"""
```

---

## 🚀 Quick Start Guide

### 1. Setup môi trường:

```bash
# Clone project và setup
git clone <project-repo>
cd gmail-auto-registration

# Cài đặt dependencies
pip install -r requirements.txt

# Setup environment
cp .env.example .env
# Sửa .env với API keys và proxy của bạn
```

### 2. Cấu hình proxy:

```python
# Sửa file config.py với proxy list của bạn
PROXY_LIST = [
    {"server": "your-proxy:port", "username": "user", "password": "pass"},
    # Thêm proxy khác...
]
```

### 3. Chạy đăng ký:

```bash
# Test với 1 tài khoản
python main.py --accounts 1 --test-mode

# Chạy batch với 5 tài khoản
python main.py --accounts 5 --concurrent 2
```

---

## ⚖️ Lưu ý pháp lý và đạo đức

### 🚨 Quan trọng:

1. **Tuân thủ ToS**: Đọc và tuân thủ Terms of Service của Google
2. **Sử dụng hợp pháp**: Chỉ sử dụng cho testing, development
3. **Không spam**: Không tạo tài khoản để spam hoặc lạm dụng
4. **Rate limiting**: Giới hạn số lượng và tần suất đăng ký
5. **Proxy hợp pháp**: Chỉ sử dụng proxy được phép

### 📋 Checklist trước khi deploy:

- [ ] Đã đọc và hiểu ToS của Google
- [ ] Có proxy hợp pháp và đáng tin cậy
- [ ] Test với số lượng nhỏ trước
- [ ] Setup monitoring và logging
- [ ] Chuẩn bị plan xử lý lỗi
- [ ] Backup và recovery strategy

---

## 🎯 Kết luận

Project này cung cấp framework hoàn chỉnh để đăng ký Gmail tự động với:

- ✅ Human-like behavior simulation
- ✅ Anti-detection techniques
- ✅ Proxy rotation và management
- ✅ AI-powered form filling
- ✅ Robust error handling
- ✅ Analytics và monitoring

**Sử dụng có trách nhiệm và tuân thủ pháp luật!** 🙏
