#!/usr/bin/env python3
"""
Test script to verify the dropdown handling fixes for Gmail registration
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from main import GmailRegistrationOrchestrator
from user_data_generator import UserDataGenerator


async def test_dropdown_handling():
    """Test the enhanced dropdown handling functionality"""
    
    print("🧪 Testing Enhanced Gmail Registration with Dropdown Handling")
    print("=" * 60)
    
    # Initialize orchestrator
    orchestrator = GmailRegistrationOrchestrator()
    
    # Test helper methods
    print("\n1. Testing helper methods:")
    
    # Test month name conversion
    test_months = [1, 7, 12]
    for month in test_months:
        month_name = orchestrator._get_month_name(month)
        print(f"   Month {month} -> {month_name}")
    
    # Test gender option conversion
    test_genders = ["male", "female", "rather not say", "prefer not to say", "other"]
    for gender in test_genders:
        gender_option = orchestrator._get_gender_option(gender)
        print(f"   Gender '{gender}' -> '{gender_option}'")
    
    print("\n2. Testing user data generation:")
    
    # Generate test user data
    user_generator = UserDataGenerator()
    user_data = user_generator.generate_user_info()
    
    print(f"   Generated user: {user_data['full_name']}")
    print(f"   Username: {user_data['username']}")
    print(f"   Birth date: {user_data['birth_month']}/{user_data['birth_day']}/{user_data['birth_year']}")
    print(f"   Gender: {user_data['gender']}")
    
    # Test month/gender conversion with generated data
    month_name = orchestrator._get_month_name(user_data['birth_month'])
    gender_option = orchestrator._get_gender_option(user_data['gender'])
    
    print(f"   Month {user_data['birth_month']} converts to: {month_name}")
    print(f"   Gender '{user_data['gender']}' converts to: '{gender_option}'")
    
    print("\n3. Testing enhanced task creation:")
    
    # Create enhanced task
    task = orchestrator._create_enhanced_task(user_data)
    
    # Check if task contains proper dropdown instructions
    dropdown_keywords = [
        "get_dropdown_options",
        "select_dropdown_option", 
        "click_element",
        "dropdown",
        month_name,
        gender_option,
        str(user_data['birth_day']),
        str(user_data['birth_year'])
    ]
    
    print("   Checking task contains required dropdown keywords:")
    for keyword in dropdown_keywords:
        if keyword in task:
            print(f"   ✅ Found: {keyword}")
        else:
            print(f"   ❌ Missing: {keyword}")
    
    # Check that old problematic methods are not used
    problematic_keywords = ["send_keys", "Type \"", "press Enter"]
    print("\n   Checking task doesn't contain problematic keywords:")
    for keyword in problematic_keywords:
        if keyword in task:
            print(f"   ❌ Found problematic: {keyword}")
        else:
            print(f"   ✅ Avoided: {keyword}")
    
    print("\n4. Testing system initialization:")
    
    # Test initialization (without actually running registration)
    try:
        init_success = await orchestrator.initialize()
        if init_success:
            print("   ✅ System initialization successful")
        else:
            print("   ❌ System initialization failed")
    except Exception as e:
        print(f"   ⚠️  System initialization error: {e}")
    
    print("\n5. Task preview (first 500 characters):")
    print("-" * 50)
    print(task[:500] + "..." if len(task) > 500 else task)
    print("-" * 50)
    
    print("\n✨ Test completed!")
    print("\nKey improvements made:")
    print("   • Added proper dropdown handling methods")
    print("   • Replaced send_keys with select_dropdown_option")
    print("   • Added month name and gender option conversion")
    print("   • Enhanced error handling for dropdown interactions")
    print("   • Added verification steps for dropdown selections")
    print("   • Improved task instructions with detailed dropdown steps")
    
    return True


def test_synchronous_methods():
    """Test synchronous helper methods"""
    print("\n🔧 Testing synchronous helper methods:")
    
    orchestrator = GmailRegistrationOrchestrator()
    
    # Test all months
    print("\n   Month conversions:")
    for i in range(1, 13):
        month_name = orchestrator._get_month_name(i)
        print(f"   {i:2d} -> {month_name}")
    
    # Test edge cases for months
    print("\n   Month edge cases:")
    edge_cases = [0, 13, -1, 15]
    for month in edge_cases:
        month_name = orchestrator._get_month_name(month)
        print(f"   {month:2d} -> {month_name}")
    
    # Test all gender options
    print("\n   Gender conversions:")
    gender_tests = [
        "male", "female", "rather not say",
        "Male", "FEMALE", "Rather Not Say",
        "prefer not to say", "other", "unknown"
    ]
    for gender in gender_tests:
        gender_option = orchestrator._get_gender_option(gender)
        print(f"   '{gender}' -> '{gender_option}'")


if __name__ == "__main__":
    print("🚀 Starting Gmail Registration Dropdown Fix Tests")
    
    # Run synchronous tests first
    test_synchronous_methods()
    
    # Run async tests
    asyncio.run(test_dropdown_handling())
    
    print("\n🎉 All tests completed!")
