"""
Human Behavior Simulation Module
Implements realistic human-like behaviors for browser automation to avoid detection.
"""

import asyncio
import random
import math
from typing import Optional
from playwright.async_api import Page
from config import Config
from constants import Timeouts, AntiDetection


class HumanBehavior:
    """Mô phỏng hành vi người dùng thật để tránh detection"""
    
    @staticmethod
    async def human_delay(min_delay: float = None, max_delay: float = None):
        """
        Random delay giống human với phân phối thực tế
        Sử dụng log-normal distribution để giống human behavior hơn
        """
        if min_delay is None:
            min_delay = Timeouts.HUMAN_ACTION["min"]
        if max_delay is None:
            max_delay = Timeouts.HUMAN_ACTION["max"]
        if not Config.HUMAN_DELAYS:
            return
            
        # Sử dụng phân phối log-normal để giống human hơn
        mu = math.log((min_delay + max_delay) / 2)
        sigma = 0.5
        
        delay = random.lognormvariate(mu, sigma)
        delay = max(min_delay, min(delay, max_delay))
        
        await asyncio.sleep(delay)
    
    @staticmethod
    async def human_typing(
        page: Page, 
        selector: str, 
        text: str, 
        mistake_rate: float = None,
        clear_first: bool = True
    ):
        """
        Gõ chậm giống human với khả năng gõ sai và sửa lại
        
        Args:
            page: Playwright page object
            selector: CSS selector của element
            text: Text cần gõ
            mistake_rate: Tỷ lệ gõ sai (0.0 - 1.0)
            clear_first: Clear field trước khi gõ
        """
        if mistake_rate is None:
            mistake_rate = AntiDetection.HUMAN_BEHAVIOR["mistake_rate"]
            
        try:
            # Click vào field
            await page.click(selector)
            await HumanBehavior.human_delay(0.3, 0.8)
            
            # Clear field nếu cần
            if clear_first:
                await page.fill(selector, "")
                await HumanBehavior.human_delay(0.2, 0.5)
            
            # Gõ từng ký tự
            for i, char in enumerate(text):
                # Thỉnh thoảng gõ sai và sửa lại
                if random.random() < mistake_rate:
                    # Gõ ký tự sai
                    wrong_char = random.choice('abcdefghijklmnopqrstuvwxyz')
                    await page.keyboard.type(wrong_char)
                    await asyncio.sleep(random.uniform(0.1, 0.3))
                    
                    # Xóa và gõ lại đúng
                    await page.keyboard.press('Backspace')
                    await asyncio.sleep(random.uniform(0.1, 0.2))
                
                # Gõ ký tự đúng
                await page.keyboard.type(char)
                
                # Delay ngẫu nhiên giữa các ký tự
                typing_delay = random.uniform(Timeouts.HUMAN_TYPING["min"], Timeouts.HUMAN_TYPING["max"])
                await asyncio.sleep(typing_delay)
                
                # Pause dài hơn sau dấu cách, chấm, hoặc @ (email)
                if char in [' ', '.', ',', '@']:
                    await asyncio.sleep(random.uniform(0.2, 0.5))
                    
        except Exception as e:
            print(f"❌ Error in human_typing: {e}")
            # Fallback to simple typing
            await page.fill(selector, text)
    
    @staticmethod
    async def random_mouse_movement(page: Page, num_movements: int = 3):
        """
        Di chuyển chuột ngẫu nhiên như human
        
        Args:
            page: Playwright page object
            num_movements: Số lần di chuyển chuột
        """
        try:
            viewport = await page.viewport_size()
            if not viewport:
                return
                
            for _ in range(num_movements):
                # Tránh các góc màn hình (human thường không click góc)
                margin = 50
                x = random.randint(margin, viewport['width'] - margin)
                y = random.randint(margin, viewport['height'] - margin)
                
                # Di chuyển chuột với tốc độ realistic
                await page.mouse.move(x, y)
                await asyncio.sleep(random.uniform(0.3, 1.0))
                
        except Exception as e:
            print(f"❌ Error in random_mouse_movement: {e}")
    
    @staticmethod
    async def random_scroll(page: Page, direction: str = "random"):
        """
        Cuộn trang ngẫu nhiên như human đọc
        
        Args:
            page: Playwright page object
            direction: "up", "down", hoặc "random"
        """
        try:
            if direction == "random":
                direction = random.choice(['up', 'down'])
            
            scroll_amount = random.randint(100, 300)
            
            if direction == 'down':
                await page.mouse.wheel(0, scroll_amount)
            else:
                await page.mouse.wheel(0, -scroll_amount)
            
            # Pause sau khi scroll như human đọc
            await HumanBehavior.human_delay(0.5, 2.0)
            
        except Exception as e:
            print(f"❌ Error in random_scroll: {e}")
    
    @staticmethod
    async def reading_pause(page: Page, element_selector: Optional[str] = None):
        """
        Dừng lại để 'đọc' như human
        
        Args:
            page: Playwright page object
            element_selector: Selector của element để hover (optional)
        """
        try:
            # Hover vào element nếu có
            if element_selector:
                try:
                    await page.hover(element_selector)
                except:
                    pass  # Ignore nếu element không tồn tại
            
            # Thời gian đọc ngẫu nhiên
            reading_time = random.uniform(Timeouts.HUMAN_READING["min"], Timeouts.HUMAN_READING["max"])
            await asyncio.sleep(reading_time)
            
        except Exception as e:
            print(f"❌ Error in reading_pause: {e}")
    
    @staticmethod
    async def natural_click(page: Page, selector: str, delay_after: bool = True):
        """
        Click tự nhiên với mouse movement và delay
        
        Args:
            page: Playwright page object
            selector: CSS selector của element cần click
            delay_after: Có delay sau khi click không
        """
        try:
            # Random mouse movement trước khi click
            await HumanBehavior.random_mouse_movement(page, 1)
            
            # Hover vào element trước khi click
            await page.hover(selector)
            await HumanBehavior.human_delay(0.2, 0.5)
            
            # Click
            await page.click(selector)
            
            if delay_after:
                await HumanBehavior.human_delay(0.5, 1.5)
                
        except Exception as e:
            print(f"❌ Error in natural_click: {e}")
            # Fallback to simple click
            await page.click(selector)
    
    @staticmethod
    async def simulate_thinking(min_seconds: float = None, max_seconds: float = None):
        """
        Mô phỏng thời gian suy nghĩ của human
        
        Args:
            min_seconds: Thời gian tối thiểu
            max_seconds: Thời gian tối đa
        """
        if min_seconds is None:
            min_seconds = Timeouts.HUMAN_THINKING["min"]
        if max_seconds is None:
            max_seconds = Timeouts.HUMAN_THINKING["max"]
            
        thinking_time = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(thinking_time)
    
    @staticmethod
    async def simulate_page_load_wait(page: Page):
        """
        Đợi trang load hoàn toàn và thêm delay realistic
        """
        try:
            # Đợi network idle
            await page.wait_for_load_state('networkidle', timeout=30000)
            
            # Thêm delay như human đọc/quan sát trang
            await HumanBehavior.human_delay(2.0, 4.0)
            
        except Exception as e:
            print(f"❌ Error waiting for page load: {e}")
            # Fallback delay
            await asyncio.sleep(3)
    
    @staticmethod
    async def random_tab_switch(page: Page):
        """
        Thỉnh thoảng switch tab như human
        (Chỉ để demo, cần browser context để thực hiện)
        """
        # This would require browser context management
        # For now, just a placeholder
        pass
    
    @staticmethod
    async def simulate_distraction():
        """
        Mô phỏng việc human bị distract (pause ngẫu nhiên)
        """
        if random.random() < 0.1:  # 10% chance
            distraction_time = random.uniform(5.0, 15.0)
            print(f"😴 Simulating distraction for {distraction_time:.1f}s...")
            await asyncio.sleep(distraction_time)
    
    @staticmethod
    async def check_element_exists(page: Page, selector: str, timeout: int = 5000) -> bool:
        """
        Kiểm tra element có tồn tại không với timeout
        
        Args:
            page: Playwright page object
            selector: CSS selector
            timeout: Timeout in milliseconds
            
        Returns:
            bool: True nếu element tồn tại
        """
        try:
            await page.wait_for_selector(selector, timeout=timeout)
            return True
        except:
            return False
    
    @staticmethod
    async def wait_and_click(page: Page, selector: str, timeout: int = 10000):
        """
        Đợi element xuất hiện và click với human behavior
        
        Args:
            page: Playwright page object
            selector: CSS selector
            timeout: Timeout in milliseconds
        """
        try:
            # Đợi element xuất hiện
            await page.wait_for_selector(selector, timeout=timeout)
            
            # Thinking pause
            await HumanBehavior.simulate_thinking(0.5, 1.5)
            
            # Natural click
            await HumanBehavior.natural_click(page, selector)
            
        except Exception as e:
            print(f"❌ Error in wait_and_click: {e}")
            raise


# Utility functions for common patterns
class HumanPatterns:
    """Common human behavior patterns"""
    
    @staticmethod
    async def fill_form_field(page: Page, selector: str, value: str, label: str = ""):
        """
        Điền form field với human behavior pattern
        """
        print(f"📝 Filling {label}: {selector}")
        
        # Thinking pause trước khi điền
        await HumanBehavior.simulate_thinking(0.5, 1.0)
        
        # Human typing
        await HumanBehavior.human_typing(page, selector, value)
        
        # Brief pause sau khi điền
        await HumanBehavior.human_delay(0.3, 0.8)
    
    @staticmethod
    async def select_dropdown_option(page: Page, selector: str, value: str, label: str = ""):
        """
        Chọn option từ dropdown với human behavior
        """
        print(f"🔽 Selecting {label}: {value}")
        
        # Click để mở dropdown
        await HumanBehavior.natural_click(page, selector)
        
        # Thinking pause
        await HumanBehavior.simulate_thinking(0.5, 1.0)
        
        # Chọn option
        await page.select_option(selector, value)
        
        # Brief pause
        await HumanBehavior.human_delay(0.3, 0.8)
    
    @staticmethod
    async def submit_form(page: Page, submit_selector: str):
        """
        Submit form với human behavior pattern
        """
        print(f"🚀 Submitting form...")
        
        # Final review pause (human thường review trước khi submit)
        await HumanBehavior.simulate_thinking(1.0, 3.0)
        
        # Click submit
        await HumanBehavior.natural_click(page, submit_selector)
        
        # Wait for navigation/response
        await HumanBehavior.simulate_page_load_wait(page) 