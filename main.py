"""
Gmail Auto Registration - Main Entry Point
Orchestrates the entire Gmail registration automation system using Browser-Use.
"""

import asyncio
import sys
import json
import click
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
from browser_use import Agent
from langchain_google_genai import ChatGoogleGenerativeAI
from dotenv import load_dotenv

# Import our modules
from config import Config
from user_data_generator import UserDataGenerator
from proxy_manager import ProxyManager
from browser_profile import BrowserProfileGenerator
from human_behavior import HumanBehavior
from constants import Messages, Timeouts, Paths


class GmailRegistrationOrchestrator:
    """Main orchestrator cho toàn bộ Gmail registration system"""

    def __init__(self):
        self.config = Config
        self.proxy_manager = None
        self.user_generator = UserDataGenerator()
        self.browser_generator = BrowserProfileGenerator()
        self.results = []
        load_dotenv()

    @staticmethod
    def _get_month_name(month_number: int) -> str:
        """Convert month number to month name for dropdown selection"""
        months = {
            1: "January", 2: "February", 3: "March", 4: "April",
            5: "May", 6: "June", 7: "July", 8: "August",
            9: "September", 10: "October", 11: "November", 12: "December"
        }
        return months.get(month_number, "January")

    @staticmethod
    def _get_gender_option(gender: str) -> str:
        """Convert gender value to dropdown option text"""
        gender_mapping = {
            "male": "Male",
            "female": "Female",
            "rather not say": "Rather not say",
            "prefer not to say": "Rather not say",
            "other": "Rather not say"
        }
        return gender_mapping.get(gender.lower(), "Rather not say")

    def _create_enhanced_task(self, user_data: Dict[str, Any]) -> str:
        """Create enhanced task instructions with proper dropdown handling"""
        month_name = self._get_month_name(user_data['birth_month'])
        gender_option = self._get_gender_option(user_data['gender'])

        return f"""
        Follow these steps precisely to register a Gmail account using proper dropdown handling:

        1. Initial Setup:
           - Navigate to https://accounts.google.com/signup
           - Wait for page to fully load (3 seconds)
           - Take screenshot as 'step_1_start.png'

        2. Basic Information:
           - Look for 'Create account' button and click it
           - Fill in first name: {user_data['first_name']}
           - Fill in last name: {user_data['last_name']}
           - Click Next button
           - Wait 2 seconds for next page to load
           - Take screenshot as 'step_2_basic_info.png'

        3. Personal Information (CRITICAL - Use dropdown methods only):
           - For birth date Month field:
             + Locate the Month dropdown field
             + Click on the Month dropdown to open it
             + Use get_dropdown_options to see all available month options
             + Use select_dropdown_option to select "{month_name}"
             + Verify selection was successful
             + Wait 1 second after selection
           - For birth date Day field:
             + Locate the Day dropdown field
             + Click on the Day dropdown to open it
             + Use select_dropdown_option to select "{user_data['birth_day']}"
             + Verify selection was successful
             + Wait 1 second after selection
           - For birth date Year field:
             + Locate the Year dropdown field
             + Click on the Year dropdown to open it
             + Use select_dropdown_option to select "{user_data['birth_year']}"
             + Verify selection was successful
             + Wait 1 second after selection
           - For gender field:
             + Locate the Gender dropdown field
             + Click on the Gender dropdown to open it
             + Use get_dropdown_options to see all available gender options
             + Use select_dropdown_option to select "{gender_option}"
             + Verify selection was successful
             + Wait 1 second after selection
           - Click Next button
           - Wait 2 seconds for next page to load
           - Take screenshot as 'step_3_personal_info.png'

        4. Username Setup:
           - Enter desired username: {user_data['username']}
           - If username is taken:
             + Look for suggested usernames in the suggestions list
             + Select the first suggested username by clicking on it
             + Note the new username for our records
           - Click Next button
           - Wait 2 seconds for next page to load
           - Take screenshot as 'step_4_username.png'

        5. Password Setup:
           - Enter password: {user_data['password']}
           - Enter confirm password: {user_data['password']}
           - Click Next button
           - Wait 2 seconds for next page to load
           - Take screenshot as 'step_5_password.png'

        6. Phone Verification Handling:
           - If phone verification screen appears:
             + Take screenshot as 'verification_required.png'
             + Stop execution and report "Phone verification required"
           - If "Skip" option is available, click it
           - Continue to next step

        7. Error Handling:
           - For any CAPTCHA challenge:
             + Take screenshot as 'captcha_encountered.png'
             + Stop execution and report "CAPTCHA detected"
           - For any error messages:
             + Take screenshot as 'error_encountered.png'
             + Stop execution and report the specific error message
           - For dropdown selection failures:
             + Take screenshot as 'dropdown_error.png'
             + Try clicking the dropdown again
             + If still fails, report "Dropdown selection failed"

        8. Success Verification:
           - If registration completes successfully:
             + Take screenshot as 'registration_complete.png'
             + Report "Registration successful"
           - Look for welcome screen or Gmail interface
           - Verify account creation was successful

        CRITICAL DROPDOWN RULES:
        - NEVER use send_keys, input_text, or typing for dropdown fields
        - ALWAYS use click_element to open dropdowns first
        - ALWAYS use get_dropdown_options to see available choices
        - ALWAYS use select_dropdown_option with exact text match
        - Wait 1-2 seconds between dropdown interactions
        - Verify each selection before proceeding
        - Take screenshots if dropdown interactions fail

        General Rules:
        - Wait 2-3 seconds between major actions
        - Take screenshots of unexpected screens
        - Stop and report any verification requests or security checks
        - Ensure each screenshot has a descriptive filename
        """
        
    async def initialize(self) -> bool:
        """
        Initialize toàn bộ system
        
        Returns:
            bool: True nếu initialization thành công
        """
        try:
            print(Messages.CONSOLE["init_start"])
            
            # Ensure directories exist
            self.config.ensure_directories()
            
            # Validate configuration
            if not self.config.validate_config():
                return False
            
            # Force disable proxy for testing
            self.config.USE_PROXY = False

            # Initialize proxy manager
            if self.config.USE_PROXY:
                self.proxy_manager = ProxyManager()
                if not await self.proxy_manager.initial_setup():
                    print(Messages.CONSOLE["proxy_failed"])
                    return False
            else:
                print(Messages.CONSOLE["proxy_disabled"])
            
            # Print configuration summary
            self.config.print_config_summary()
            
            print(Messages.CONSOLE["init_success"])
            return True
            
        except Exception as e:
            print(f"{Messages.CONSOLE['init_failed']}: {e}")
            return False
    
    async def register_single_account(self, account_id: int = 1) -> Dict[str, Any]:
        """
        Register a single Gmail account

        Args:
            account_id: Account ID (for logging)
            
        Returns:
            Dict containing registration results
        """
        start_time = datetime.now()
        print(f"\n{Messages.CONSOLE['registration_start']} #{account_id}")
        
        try:
            # Generate user data
            user_data = self.user_generator.generate_user_info()
            print(f"{Messages.CONSOLE['user_generated']}: {user_data['full_name']} ({user_data['username']})")
            
            # Get proxy if enabled
            proxy_config = None
            if self.proxy_manager:
                proxy_config = await self.proxy_manager.get_working_proxy()
                if not proxy_config:
                    return {
                        "success": False,
                        "error": Messages.ERRORS["no_proxy"],
                        "account_id": account_id,
                        "user_data": user_data,
                        "timestamp": start_time.isoformat()
                    }
                print(f"{Messages.CONSOLE['proxy_using']}: {proxy_config.server}")
            
            # Create browser profile
            browser_profile = self.browser_generator.create_human_browser_profile(proxy_config)
            print(f"🌍 Browser timezone: {browser_profile['timezone_id']}")
            print(f"📱 Viewport: {browser_profile['viewport']}")
            
            # Start registration process
            result = await self._perform_registration(user_data, browser_profile, account_id)
            
            # Calculate duration
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            result["duration_seconds"] = duration
            result["account_id"] = account_id
            
            if result["success"]:
                print(f"{Messages.CONSOLE['registration_success']} #{account_id} in {duration:.1f}s")
                print(f"📧 Email: {result.get('email', 'N/A')}")
            else:
                print(f"{Messages.CONSOLE['registration_failed']} #{account_id}: {result.get('error', Messages.ERRORS['unknown_error'])}")
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "account_id": account_id,
                "timestamp": start_time.isoformat(),
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    async def _perform_registration(self, user_data: Dict[str, Any], browser_profile: Dict[str, Any], account_id: int) -> Dict[str, Any]:
        """
        Perform the actual registration process using Browser-Use
        
        Args:
            user_data: User data dict
            browser_profile: Browser profile dict
            account_id: Account ID
            
        Returns:
            Registration result dict
        """
        try:
            print(Messages.CONSOLE["browser_start"])
            
            # Initialize Gemini LLM
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.0-flash",
                temperature=0.7
            )
            
            # Create enhanced task instructions with proper dropdown handling
            task = self._create_enhanced_task(user_data)
            
            # Configure sensitive data
            sensitive_data = {
                "first_name": user_data['first_name'],
                "last_name": user_data['last_name'],
                "username": user_data['username'],
                "password": user_data['password'],
                "birth_date": f"{user_data['birth_month']} {user_data['birth_day']}, {user_data['birth_year']}"
            }
            
            # Create agent
            agent = Agent(
                task=task,
                llm=llm,
                sensitive_data=sensitive_data,
                use_vision=True
            )
            
            # Run the agent
            agent_result = await agent.run()
            
            # Check if agent reported any errors
            if not agent_result.get("success", False):
                return {
                    "success": False,
                    "error": agent_result.get("error", "Unknown agent error"),
                    "user_data": user_data,
                    "timestamp": datetime.now().isoformat(),
                    "screenshots_path": str(self.config.SCREENSHOTS_DIR / f"account_{account_id}")
                }
            
            # Process successful result
            email = f"{user_data['username']}@gmail.com"
            result = {
                "success": True,
                "email": email,
                "user_data": user_data,
                "proxy_used": browser_profile.get('proxy'),
                "timestamp": datetime.now().isoformat(),
                "screenshots_path": str(self.config.SCREENSHOTS_DIR / f"account_{account_id}"),
                "agent_details": agent_result  # Include agent result details
            }
            await self._save_account_data(result)
            return result

        except Exception as e:
            print(f"❌ An error occurred during browser automation: {e}")
            return {
                "success": False,
                "error": str(e),
                "user_data": user_data,
                "timestamp": datetime.now().isoformat()
            }

    async def register_batch_accounts(self, num_accounts: int, max_concurrent: int = None) -> List[Dict[str, Any]]:
        """
        Register multiple accounts simultaneously

        Args:
            num_accounts: Number of accounts to register
            max_concurrent: Maximum number of concurrent registrations
            
        Returns:
            List of registration results
        """
        if max_concurrent is None:
            max_concurrent = self.config.MAX_CONCURRENT_REGISTRATIONS
            
        print(f"\n🚀 Starting batch registration of {num_accounts} accounts")
        print(f"⚙️ Max concurrent: {max_concurrent}")
        print(f"⏱️ Delay between registrations: {self.config.DELAY_BETWEEN_REGISTRATIONS}s")
        
        # Create semaphore to limit concurrent registrations
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def register_with_semaphore(account_id: int):
            try:
                async with semaphore:
                    result = await self.register_single_account(account_id)
                    
                    # Delay between registrations
                    if account_id < num_accounts:
                        delay = self.config.DELAY_BETWEEN_REGISTRATIONS
                        print(f"⏰ Waiting {delay}s before next registration...")
                        await asyncio.sleep(delay)
                    
                    return result
            except Exception as e:
                error_msg = f"Registration task {account_id} failed: {str(e)}"
                print(f"❌ {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "account_id": account_id,
                    "timestamp": datetime.now().isoformat(),
                    "exception_type": type(e).__name__,
                    "exception_details": str(e)
                }
        
        # Create tasks for all accounts
        tasks = []
        for i in range(1, num_accounts + 1):
            tasks.append(register_with_semaphore(i))
            
        # Run all tasks concurrently and wait for completion
        results = await asyncio.gather(*tasks)
        
        # Save batch results
        await self._save_batch_results(results)
        
        return results
    
    async def _save_account_data(self, account_data: Dict[str, Any]):
        """Save account data to JSON file"""
        try:
            # Create filename with timestamp and account ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            account_id = account_data.get("account_id", "unknown")
            filename = f"account_{account_id}_{timestamp}.json"
            
            # Save to accounts directory
            filepath = self.config.ACCOUNTS_DIR / filename
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(account_data, f, indent=2, ensure_ascii=False)
                
            print(f"💾 Account data saved to {filepath}")
            
        except Exception as e:
            print(f"⚠️ Failed to save account data: {e}")
    
    async def _save_batch_results(self, results: List[Dict[str, Any]]):
        """Save batch results to JSON file"""
        try:
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"batch_results_{timestamp}.json"
            
            # Save to results directory
            filepath = self.config.RESULTS_DIR / filename
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
                
            print(f"💾 Batch results saved to {filepath}")
            
        except Exception as e:
            print(f"⚠️ Failed to save batch results: {e}")


@click.command()
@click.option('--accounts', '-n', default=1, help='Number of accounts to register')
@click.option('--concurrent', '-c', default=None, type=int, help='Max concurrent registrations')
@click.option('--test-mode', '-t', is_flag=True, help='Run in test mode (no actual registration)')
@click.option('--config-check', is_flag=True, help='Check configuration and exit')
@click.option('--proxy-test', is_flag=True, help='Test proxies and exit')
def main(accounts: int, concurrent: int, test_mode: bool, config_check: bool, proxy_test: bool):
    """Gmail Auto Registration Tool"""
    
    async def run_async():
        orchestrator = GmailRegistrationOrchestrator()
        
        # Initialize system
        if not await orchestrator.initialize():
            sys.exit(1)
            
        # Handle special modes
        if config_check:
            print("✅ Configuration check passed")
            return
            
        if proxy_test:
            if not orchestrator.proxy_manager:
                print("❌ Proxy testing requires proxy support to be enabled")
                return
            await orchestrator.proxy_manager.test_all_proxies()
            return
            
        if test_mode:
            print("🧪 Running in TEST MODE - no actual registrations will be performed")
            
        # Start registration process
        if accounts > 1:
            results = await orchestrator.register_batch_accounts(accounts, concurrent)
            success_count = len([r for r in results if r["success"]])
            print(f"\n✨ Batch registration completed: {success_count}/{accounts} successful")
        else:
            result = await orchestrator.register_single_account()
            if result["success"]:
                print("\n✨ Registration completed successfully!")
            else:
                print("\n❌ Registration failed!")
                sys.exit(1)
    
    # Run async code
    asyncio.run(run_async())


if __name__ == "__main__":
    main() 